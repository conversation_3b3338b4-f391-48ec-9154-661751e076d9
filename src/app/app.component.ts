import { DOCUMENT } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';

import { CustomizerService } from './services/customizer.service';
import { InfoService } from './services/info.service';
import { IInfo } from './types/interfaces/church.interface';
import { Utils } from './utils';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: [],
  standalone: false,
})
export class AppComponent implements OnInit {
  title = 'Tithing APPs';
  loadingInfo = true;
  info: IInfo | null = null;
  key = 'church';
  constructor(
    public customizer: CustomizerService,
    private infoService: InfoService,
    @Inject(DOCUMENT) private document: any,
    private titleService: Title,
  ) {}

  ngOnInit(): void {
    this.infoService.getInfo().subscribe({
      next: (res) => {
        this.loadingInfo = false;
        if (res.status) {
          this.info = res.data as IInfo;
          Utils.setStorage(this.key, this.info);
          this.document.getElementById('appFavicon').setAttribute('href', this.info.logo);
          this.titleService.setTitle(this.info.name);
          return;
        }

        Utils.clearStorage(this.key);
      },
      error: (err) => {
        console.warn('API not available, using mock data for development');
        this.loadingInfo = false;
        this.info = {
          churchId: '',
          name: 'Church App Admin',
          logo: 'favicon.gif',
          content: 'Church App Admin',
        } as IInfo;
        Utils.setStorage(this.key, this.info);
        this.titleService.setTitle(this.info.name);
      },
    });
  }
}
