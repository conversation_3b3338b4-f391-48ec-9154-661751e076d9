import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';

import { AdminService } from '../services/admin.service';
import { CustomizerService } from '../services/customizer.service';
import { IInfo } from './../types/interfaces/church.interface';
import { Utils } from './../utils/index';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss'],
  standalone: false,
})
export class ForgotPasswordComponent implements OnInit {
  msg = '';

  loading: boolean = false;
  email = new UntypedFormControl('', [Validators.required, Validators.email]);
  info!: IInfo;

  constructor(
    public snackBar: MatSnackBar,
    private route: ActivatedRoute,
    private router: Router,
    private service: AdminService,
    public customizer: CustomizerService,
  ) {}

  ngOnInit(): void {
    this.info = Utils.getInfo();
  }

  submit() {
    try {
      this.loading = true;
      this.service.forgotPassword(this.email.value).subscribe({
        next: (res) => {
          if (!res.status) {
            this.loading = false;
            this.openSnackBar(res.message, 'Try again');
            return;
          }

          this.loading = false;
          this.openSnackBar(res.message, 'ok');
          setTimeout(() => {
            this.router.navigate(['/login']);
          }, 2100);
        },
        error: (err) => {
          this.loading = false;
          this.openSnackBar(err.error.message, 'Try again');
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  getErrorMessage(): string {
    return this.email.hasError('required')
      ? 'You must enter a value'
      : this.email.hasError('email')
      ? 'Not a valid email'
      : '';
  }

  openSnackBar(message: string, action: string) {
    this.snackBar.open(message, action, {
      duration: 2000,
    });
  }
}
