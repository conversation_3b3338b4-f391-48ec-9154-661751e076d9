export interface IMediaCollection {
  _id: string;
  title: string;
  shortTitle: string;
  author: string;
  shortAuthor: string;
  duration: string;
  media: string;
  thumbnail: string;
  channelId: string;
  description: string;
  vimeoId: string;
  type: string;
  mediaDuration: string;
  playLink: string;
  isLoading?: boolean;
  status: string;
}

export interface IMediaUpload {
  mediaData: IMediaCollection;
  uploadImage: File;
  uploadMedia: File;
}

export interface IPlay {
  status: string;
  play: {
    progressive: [
      {
        link: string;
      },
    ];
  };
}

export interface IVimeoUploadUrlResponse {
  status: string;
  vimeoId: string;
  upload: {
    status: string;
    upload_link: string;
    form: string;
    approach: string;
    size: number;
    redirect_url: string | null;
    link: string | null;
  };
  uri: string;
  name: string;
  description: string;
  type: string;
  link: string;
  player_embed_url: string;
  duration: number;
  width: number;
  language: string | null;
  height: number;
  embed: unknown;
}
