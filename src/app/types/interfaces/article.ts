export interface IArticleCollection {
  _id: string;
  title: string;
  author: string;
  shortAuthor: string;
  published: boolean;
  createdAt: Date;
  articleData: string;
  shortTitle: string;
  thumbnail?: string;
  articleImage?: string;
}

export interface IArticleData {
  _id: string;
  title: string;
  author: string;
  published: boolean;
  articleData: string;
  thumbnail?: string;
  articleImage?: string;
}
