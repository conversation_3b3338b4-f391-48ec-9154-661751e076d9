export interface IChurchCollection {
  _id: string;
  name: string;
  email: string;
  domain: string;
  content: string;
  isActive: true;
  createdAt: string;
  updatedAt: string;
  coverImage: string;
  isActivationCompleted: boolean;
  logo: string;
  mobile: string;
  password: string;
}

export interface IInfo {
  logo: string;
  churchId: string;
  content: string;
  name: string;
}

export interface ILoginChurchCollection {
  _id: string;
  name: string;
  email: string;
  domain: string;
  content: string;
  isActive: true;
  createdAt: string;
  updatedAt: string;
  coverImage: string;
  isActivationCompleted: boolean;
  logo: string;
  mobile: string;
  password: string;
  login: boolean;
}
