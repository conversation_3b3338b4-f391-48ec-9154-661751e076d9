.img {
  width: 70px;
  height: 70px;
}

.img-notification {
  width: 200px;
  height: 200px;
}

.clear-image {
  padding: 0px !important;
  color: red;
  width: 200px;
}

.mat-date-picker-input {
  width: 85%;
}

.mat-icon-clear {
  position: relative;
  float: right;
  top: -3px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.54);
  font-size: 20px;
}

.mat-icon-clear:active {
  color: #fb9778;
}
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-100 {
  flex: 1 1 100%;
}

.flex-25-md {
  flex: 1 1 100%;
}

.flex-75-md {
  flex: 1 1 100%;
}

.flex-10-md {
  flex: 1 1 100%;
}

.flex-20-md {
  flex: 1 1 100%;
}

/* Larger screens (similar to .gt-md or .gt-sm in Angular Flex) */
@media (min-width: 960px) {
  .flex-25-md {
    flex: 1 1 25%;
  }

  .flex-75-md {
    flex: 1 1 75%;
  }

  .flex-10-md {
    flex: 1 1 10%;
  }

  .flex-20-md {
    flex: 1 1 20%;
  }
}

.gap-40 {
  gap: 40px;
}
