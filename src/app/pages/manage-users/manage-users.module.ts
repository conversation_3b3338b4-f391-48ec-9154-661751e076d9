import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxDatatableModule } from '@siemens/ngx-datatable';
// import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { ManageUserComponent } from 'src/app/components/users/manage-user/manage-user.component';
import { ManageUserDialogComponent } from 'src/app/components/users/manage-user-dialog/manage-user-dialog.component';
import { MaterialModule } from 'src/app/material/material.module';

import { ManageUsersComponent } from './manage-users.component';
import { manageUserRoutes } from './manage-users.routing';

@NgModule({
  declarations: [ManageUsersComponent, ManageUserComponent, ManageUserDialogComponent],
  imports: [
    RouterModule.forChild(manageUserRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    // FlexLayoutModule,
    FeatherModule.pick(allIcons),
    NgxDatatableModule,
    SpinnerModule,
  ],
})
export class ManageUsersModule {}
