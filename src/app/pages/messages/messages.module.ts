import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxDatatableModule } from '@siemens/ngx-datatable';
// import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { MapUrlFromTextDirectiveModule } from 'src/app/directives/map-url-from-text.directive';
import { MaterialModule } from 'src/app/material/material.module';

import { MessagesComponent } from './messages.component';
import { messageRoutes } from './messages.routing';

@NgModule({
  declarations: [MessagesComponent],
  imports: [
    RouterModule.forChild(messageRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    // FlexLayoutModule,
    FeatherModule.pick(allIcons),
    NgxDatatableModule,
    SpinnerModule,
    MapUrlFromTextDirectiveModule,
  ],
})
export class MessagesModule {}
