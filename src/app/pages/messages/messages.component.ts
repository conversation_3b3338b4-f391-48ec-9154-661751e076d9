import { animate, state, style, transition, trigger } from '@angular/animations';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, FormGroupDirective, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { MessageComponent } from 'src/app/components/message/message.component';
import { MessagesService } from 'src/app/services/messages.service';
import { CommentObject, IMessage } from 'src/app/types/interfaces/message.interface';

@Component({
  selector: 'app-messages',
  templateUrl: './messages.component.html',
  styleUrls: ['./messages.component.scss'],
  standalone: false,
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class MessagesComponent implements OnInit, AfterViewInit {
  isPageLoading: boolean = false;
  buttonLoading: boolean = false;
  emptyDataMsg: Boolean = false;

  messageForm: UntypedFormGroup = new UntypedFormGroup({});
  messageDataSubmitted: Boolean = false;
  messageDate!: Date;
  messageFormData!: IMessage;

  @ViewChild(MatTable, { static: true }) table: MatTable<any> = Object.create(null);
  displayedColumns: string[] = ['title', 'message', 'date', 'action'];
  commentColumns: string[] = ['name', 'comment', 'email', 'action'];
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator = Object.create(null);
  expandedElement: CommentObject | null = null;

  totalRows = 0;
  pageSize = 5;
  currentPage = 0;

  constructor(
    public snackBar: MatSnackBar,
    private fb: UntypedFormBuilder,
    public dialog: MatDialog,
    private messageService: MessagesService,
  ) {}

  ngOnInit(): void {
    this.messageForm = this.fb.group({
      title: ['', Validators.required],
      content: ['', Validators.required],
    });

    this.getMessagesByPage(this.currentPage, this.pageSize);
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
  }

  get messageFormControl(): { [key: string]: AbstractControl } {
    return this.messageForm.controls;
  }

  pageChanged(event: PageEvent) {
    try {
      this.pageSize = event.pageSize;
      this.currentPage = event.pageIndex;

      this.getMessagesByPage(this.currentPage, this.pageSize);
    } catch (error) {
      console.error(error);
    }
  }

  openDialog(action: string, data: object, msgId?: string): void {
    let obj = { ...data, action, msgId };
    const dialogRef = this.dialog.open(MessageComponent, { data: obj });
    dialogRef.afterClosed().subscribe(() => {
      this.getMessagesByPage(this.currentPage, this.pageSize);
    });
  }

  messageFormSubmit(formDirective: FormGroupDirective) {
    try {
      this.buttonLoading = true;
      this.messageDataSubmitted = true;
      if (this.messageForm.invalid) {
        this.buttonLoading = false;
        return;
      }

      this.messageFormData = this.messageForm.value;

      const msgData = {
        ...this.messageFormData,
        date: new Date().toDateString(),
      };

      this.messageService.messageInsert(msgData).subscribe({
        next: (res) => {
          if (!res.status) {
            this.buttonLoading = false;
            this.openSnackBar(res.message, 'Try again');
            return;
          }

          this.snackBar.open(res.message, 'ok', {
            duration: 3000,
          });
          this.buttonLoading = false;
          this.messageDataSubmitted = false;
          this.getMessagesByPage(this.currentPage, this.pageSize);
          formDirective.resetForm();
        },
        error: (err) => {
          this.buttonLoading = false;
          this.snackBar.open('Something went wrong', 'ok', {
            duration: 3000,
          });
        },
      });
    } catch (error) {
      console.error(error);
      this.buttonLoading = false;
      this.openSnackBar('Something went wrong', 'Try again');
    }
  }

  getMessagesByPage(page: number, pageSize: number) {
    try {
      this.isPageLoading = true;
      this.messageService.getMessagesByPage(page, pageSize).subscribe({
        next: (res) => {
          this.isPageLoading = false;
          this.messageDataSubmitted = false;

          if (!res.status || !res.data) {
            this.emptyDataMsg = true;
            this.isPageLoading = false;
            return;
          }

          this.emptyDataMsg = false;
          this.isPageLoading = false;

          this.dataSource.data = res.data.result;

          setTimeout(() => {
            this.paginator.pageIndex = this.currentPage;
            this.paginator.length = res.data?.total ?? 0;
          });
        },
        error: (err) => {
          this.isPageLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (error) {
      this.isPageLoading = false;
      console.error(error);
    }
  }

  openSnackBar(message: string, action: string) {
    this.snackBar.open(message, action, {
      duration: 2000,
    });
  }
}
