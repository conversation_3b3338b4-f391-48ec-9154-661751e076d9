import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, FormGroupDirective, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import moment from 'moment';
import { EventComponent } from 'src/app/components/event/event.component';
import { EventService } from 'src/app/services/event.service';
import { IEvent } from 'src/app/types/interfaces/event.interface';

@Component({
  selector: 'app-events',
  templateUrl: './events.component.html',
  styleUrls: ['./events.component.scss'],
  standalone: false,
})
export class EventsComponent implements OnInit, AfterViewInit {
  isPageLoading: boolean = false;

  eventForm: UntypedFormGroup = new UntypedFormGroup({});
  eventDataSubmitted: Boolean = false;
  eventDate!: Date;
  fromTime!: string;
  toTime!: string;
  eventFormData!: IEvent;

  emptyDataMsg: Boolean = false;
  selectedDate?: Date;
  buttonLoading: boolean = false;

  img?: string | ArrayBuffer | null;
  imageUpload?: File | string;

  todayDate = new Date();

  @ViewChild(MatTable, { static: true }) table: MatTable<any> = Object.create(null);
  displayedColumns: string[] = ['title', 'description', 'date', 'action'];
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator = Object.create(null);

  totalRows = 0;
  pageSize = 10;
  currentPage = 0;
  constructor(
    private fb: UntypedFormBuilder,
    public dialog: MatDialog,
    public snackBar: MatSnackBar,
    private eventService: EventService,
  ) {}

  ngOnInit(): void {
    this.getEvents(this.currentPage, this.pageSize);
    this.eventForm = this.fb.group({
      title: ['', Validators.required],
      description: ['', Validators.required],
      eventSelectDate: ['', Validators.required],
    });
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
  }

  getDate(date: Date) {
    this.eventDate = date;
  }

  timeFieldFrom(event: any) {
    this.fromTime = String(event);
  }
  timeFieldTo(event: any) {
    this.toTime = String(event);
  }

  pageChanged(event: PageEvent) {
    try {
      this.pageSize = event.pageSize;
      this.currentPage = event.pageIndex;
      this.getEvents(this.currentPage, this.pageSize);
    } catch (error) {
      console.error(error);
    }
  }

  openDialog(action: string, data: object): void {
    let obj = { ...data, action };
    const dialogRef = this.dialog.open(EventComponent, { data: obj });
    dialogRef.afterClosed().subscribe(() => {
      this.getEvents(this.currentPage, this.pageSize);
    });
  }

  eventFormSubmit(formDirective: FormGroupDirective) {
    try {
      this.buttonLoading = true;
      this.eventDataSubmitted = true;
      if (this.eventForm.invalid) {
        this.buttonLoading = false;
        return;
      }

      if (!this.imageUpload) {
        this.snackBar.open('Please, upload event image', 'ok', {
          duration: 5000,
        });
        this.buttonLoading = false;
        return;
      }

      this.eventFormData = this.eventForm.value;

      const formatDate = [
        (this.eventDate.getMonth() + 1).toString().padStart(2, '0'),
        this.eventDate.getDate().toString().padStart(2, '0'),
        this.eventDate.getFullYear(),
      ].join('-');

      const startDateTime = moment(formatDate + ' ' + this.fromTime);
      const endDateTime = moment(formatDate + ' ' + this.toTime);

      const eventData: IEvent = {
        ...this.eventFormData,
        startDate: startDateTime.format(),
        endDate: endDateTime.format(),
      };

      const dataPass = {
        event: eventData,
        image: this.imageUpload,
      };
      this.eventService.eventInsert(dataPass.event, dataPass.image).subscribe({
        next: (res) => {
          if (!res.status) {
            this.buttonLoading = false;
            this.openSnackBar(res.message, 'Try again');
            return;
          }

          this.buttonLoading = false;
          this.openSnackBar('Event Created', 'Success');
          this.getEvents(this.currentPage, this.pageSize);
          formDirective.resetForm();
          this.img = '';
          this.imageUpload = '';
        },
        error: (err) => {
          this.buttonLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      console.error(err);
      this.buttonLoading = false;
      this.openSnackBar('Something went wrong', 'Try again');
    }
  }

  getEvents(page: number, pageSize: number) {
    try {
      this.isPageLoading = true;
      this.eventService.getEventsByPage(page, pageSize).subscribe({
        next: (res) => {
          this.isPageLoading = false;
          this.eventDataSubmitted = false;

          if (!res.status || !res.data) {
            this.emptyDataMsg = true;
            this.isPageLoading = false;
            return;
          }

          this.emptyDataMsg = false;
          this.isPageLoading = false;

          this.dataSource.data = res.data.result;

          setTimeout(() => {
            this.paginator.pageIndex = this.currentPage;
            this.paginator.length = res.data?.total ?? 0;
          });
        },
        error: (err) => {
          this.isPageLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (error) {
      this.isPageLoading = false;
      console.error(error);
    }
  }
  get eventFormControl(): { [key: string]: AbstractControl } {
    return this.eventForm.controls;
  }

  selectImage(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    let fileList: FileList | null = element.files;

    if (!fileList || fileList.length === 0) {
      return;
    }

    const mimeType = fileList[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(fileList[0]);
    reader.onload = (_event) => {
      this.img = reader.result;
    };
    this.imageUpload = <File>fileList[0];
  }

  openSnackBar(message: string, action: string) {
    this.snackBar.open(message, action, {
      duration: 2000,
    });
  }

  clearUploads() {
    this.imageUpload = undefined as unknown as File;
    this.img = '';
  }
}
