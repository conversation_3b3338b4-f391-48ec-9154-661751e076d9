<div *ngIf="isPageLoading" class="flex-center">
  <app-spinner></app-spinner>
</div>

<div class="flex-row-wrap events-block">
  <div class="flex-item flex-100">
    <mat-card>
      <mat-card-content>
        <mat-card-title>Create Event</mat-card-title>

        <form
          #formDirective="ngForm"
          [formGroup]="eventForm"
          class="basic-form"
          (ngSubmit)="eventFormSubmit(formDirective)">
          <div class="flex-row-wrap">
            <div class="flex-item flex-100 flex-75-md">
              <div>
                <mat-form-field>
                  <input
                    matInput
                    formControlName="title"
                    placeholder="Title"
                    name="title"
                    [ngClass]="{
                      'is-invalid': eventDataSubmitted && eventFormControl['title'].errors
                    }" />
                </mat-form-field>
                <div *ngIf="eventDataSubmitted && eventFormControl['title'].errors" class="invalid-feedback">
                  <div *ngIf="eventFormControl['title'].errors['required']">Title is required</div>
                </div>
              </div>

              <div>
                <mat-form-field>
                  <input
                    matInput
                    formControlName="description"
                    name="description"
                    placeholder="Description"
                    [ngClass]="{
                      'is-invalid': eventDataSubmitted && eventFormControl['description'].errors
                    }" />
                </mat-form-field>
                <div *ngIf="eventDataSubmitted && eventFormControl['description'].errors" class="invalid-feedback">
                  <div *ngIf="eventFormControl['description'].errors['required']">Description is required</div>
                </div>
              </div>

              <div class="image-section">
                <div class="image-preview">
                  <img
                    class="img-event"
                    [src]="img"
                    onerror="this.src='assets/images/backgrounds/thumbnail-dummy-image.svg'" />
                  <button
                    class="clear-image"
                    *ngIf="imageUpload"
                    matSuffix
                    mat-raised-button
                    aria-label="Clear"
                    (click)="clearUploads()">
                    <mat-icon>highlight_off</mat-icon>Clear
                  </button>
                </div>

                <div class="upload-controls">
                  <div>
                    <button mat-raised-button color="accent" class="btn-blue">
                    <label for="images">Select an event image</label>
                    <input
                      type="file"
                      id="images"
                      accept="image/png, image/jpeg, image/jpg"
                      required
                      (change)="selectImage($event)" />
                  </button>
                  </div>
                  <label class="block image-informative-cls m-l-5 m-t-10">Acceptable type: png, jpg, jpeg</label>
                  <label class="block image-informative-cls m-l-5">Upload limit: 15MB</label>
                  <label class="block image-informative-cls m-l-5">Width: 200px Height: 200px</label>
                </div>
              </div>

              <div>
                <mat-form-field class="date-field">
                  <mat-label>Event Date</mat-label>
                  <input
                    matInput
                    [readonly]="true"
                    [min]="todayDate"
                    [matDatepicker]="picker"
                    autocomplete="off"
                    (ngModelChange)="getDate($event)"
                    formControlName="eventSelectDate" />
                  <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                  <mat-datepicker #picker color="primary"></mat-datepicker>
                </mat-form-field>
              </div>

              <div *ngIf="eventDate" class="time-row">
                <div class="time-field">
                  <mat-label>Time Start</mat-label>
                  <ngx-timepicker-field (timeChanged)="timeFieldFrom($event)"></ngx-timepicker-field>
                </div>
                <div class="time-field">
                  <mat-label>Time End</mat-label>
                  <ngx-timepicker-field (timeChanged)="timeFieldTo($event)"></ngx-timepicker-field>
                </div>
              </div>

              <div class="m-t-15">
                <button
                  mat-raised-button
                  [class.spinner]="buttonLoading"
                  [disabled]="!eventForm.valid || buttonLoading"
                  color="primary" class="orange-primary">
                  Submit
                </button>
              </div>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<div class="flex-row-wrap">
  <div class="flex-item flex-100">
    <mat-card>
      <mat-card-content>
        <div class="flex-row-wrap flex-space-between-center gap-40">
          <div class="flex-item flex-10-md">
            <mat-card-title class="m-l-10">Events</mat-card-title>
          </div>
        </div>

        <div class="table-responsive">
          <div *ngIf="emptyDataMsg; then eventEmpty; else tableBlock"></div>

          <ng-template #eventEmpty>
            <h3 *ngIf="emptyDataMsg" class="mat-h3 text-center">Events Not Found</h3>
          </ng-template>

          <ng-template #tableBlock>
            <table mat-table [dataSource]="dataSource" class="w-100">
              <ng-container matColumnDef="title">
                <th mat-header-cell *matHeaderCellDef>Title</th>
                <td mat-cell *matCellDef="let element">
                  <div class="d-flex align-items-center">
                    <img
                      class="img"
                      width="50"
                      height="50"
                      alt="event-image"
                      [src]="element.image"
                      onerror="this.src='assets/images/backgrounds/thumbnail-dummy-image.svg'" />
                    <div class="m-l-15">
                      <p class="lh-md m-t-0">{{ element.title | titlecase }}</p>
                    </div>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="description">
                <th mat-header-cell *matHeaderCellDef>Description</th>
                <td mat-cell *matCellDef="let element">
                  <p class="lh-md m-t-0">{{ element.description | titlecase | slice: 0:50 }}</p>
                </td>
              </ng-container>

              <ng-container matColumnDef="date">
                <th mat-header-cell *matHeaderCellDef>Date</th>
                <td mat-cell *matCellDef="let element">
                  <p class="lh-md m-t-0">{{ element.startDate | date: ' MMM d, y ' }}</p>
                </td>
              </ng-container>

              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef>Action</th>
                <td mat-cell *matCellDef="let element" class="action-link">
                  <a (click)="openDialog('Delete Event', element)" class="m-r-10 cursor-pointer" title="Delete Event">
                    <i-feather name="trash" class="text-danger feather-18"></i-feather>
                  </a>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            </table>
          </ng-template>

          <mat-paginator
            #paginator
            [length]="totalRows"
            [pageIndex]="currentPage"
            [pageSize]="pageSize"
            (page)="pageChanged($event)"
            [pageSizeOptions]="[10, 20, 30, 40]"
            aria-label="Select page">
          </mat-paginator>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
