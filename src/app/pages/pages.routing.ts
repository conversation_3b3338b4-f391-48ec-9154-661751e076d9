import { Routes } from '@angular/router';

export const PagesRoutes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'home',
        loadChildren: () => import('./home/<USER>').then((m) => m.HomeModule),
      },
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full',
      },

      {
        path: 'video',
        loadChildren: () => import('./medias/medias.module').then((m) => m.MediasModule),
      },

      {
        path: 'audio',
        loadChildren: () => import('./medias/medias.module').then((m) => m.MediasModule),
      },

      {
        path: 'category',
        loadChildren: () => import('./channel/channel.module').then((m) => m.ChannelModule),
      },

      {
        path: 'dashboard',
        loadChildren: () => import('./dashboard/dashboard.module').then((m) => m.DashboardModule),
      },

      {
        path: 'profile',
        loadChildren: () => import('./user-profile/user-profile.module').then((m) => m.UserProfileModule),
      },

      {
        path: 'notification',
        loadChildren: () => import('./notification/notification.module').then((m) => m.NotificationModule),
      },

      {
        path: 'manage-users',
        loadChildren: () => import('./manage-users/manage-users.module').then((m) => m.ManageUsersModule),
      },
      {
        path: 'manage-Admins',
        loadChildren: () => import('./manage-admin/manage-admin.module').then((m) => m.ManageAdminModule),
      },
      {
        path: 'messages',
        loadChildren: () => import('./messages/messages.module').then((m) => m.MessagesModule),
      },
      {
        path: 'events',
        loadChildren: () => import('./events/event-module.module').then((m) => m.EventModuleModule),
      },
      {
        path: 'settings',
        loadChildren: () => import('./settings/settings.module').then((m) => m.SettingsModule),
      },
      {
        path: 'article',
        loadChildren: () => import('./manage-article/manage-article.module').then((m) => m.ManageArticleModule),
      },
    ],
  },
];
