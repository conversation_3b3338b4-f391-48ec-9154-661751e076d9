/* Common Flex Utilities */
.flex-row-wrap {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/* Default full width */
.flex-item {
  flex: 1 1 100%;
}

/* Responsive behavior */
@media (min-width: 600px) {
  /* gt-xs */
  .flex-50-xs {
    flex: 1 1 50%;
  }
}

@media (min-width: 960px) {
  /* gt-md */
  .flex-50-md {
    flex: 1 1 50%;
  }

  .flex-100 {
    flex: 1 1 100%;
  }
}
