<div *ngIf="isLoading" class="flex-center">
  <app-spinner></app-spinner>
</div>

<div class="flex-row-wrap">
  <div class="flex-item flex-50-md">
    <app-welcome-card></app-welcome-card>

    <div class="flex-row-wrap">
      <div class="flex-item flex-50-xs">
        <app-audio-list-card [count]="audiosCount"></app-audio-list-card>
      </div>
      <div class="flex-item flex-50-xs">
        <app-video-list-card [count]="videosCount"></app-video-list-card>
      </div>
      <div class="flex-item flex-50-xs">
        <app-user-list-card [count]="usersCount"></app-user-list-card>
      </div>
      <div class="flex-item flex-50-xs">
        <app-tbd-list-card [count]="verifiedUserCount"></app-tbd-list-card>
      </div>
    </div>
  </div>

  <div class="flex-item flex-50-md">
    <app-total-storage-space></app-total-storage-space>
  </div>
</div>

<div class="flex-row-wrap">
  <div class="flex-item flex-100">
    <app-logged-users-list-card></app-logged-users-list-card>
  </div>
</div>
