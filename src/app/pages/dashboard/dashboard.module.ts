import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxDatatableModule } from '@siemens/ngx-datatable';
// import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { NgApexchartsModule } from 'ng-apexcharts';
import { AudioListCardComponent } from 'src/app/components/dashboard/audio-list-card/audio-list-card.component';
import { LoggedUsersListCardComponent } from 'src/app/components/dashboard/logged-users-list-card/logged-users-list-card.component';
import { TbdListCardComponent } from 'src/app/components/dashboard/tbd-list-card/tbd-list-card.component';
import { TotalStorageSpaceComponent } from 'src/app/components/dashboard/total-storage-space/total-storage-space.component';
import { UserListCardComponent } from 'src/app/components/dashboard/user-list-card/user-list-card.component';
import { VideoListCardComponent } from 'src/app/components/dashboard/video-list-card/video-list-card.component';
import { WelcomeCardComponent } from 'src/app/components/dashboard/welcome-card/welcome-card.component';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { MaterialModule } from 'src/app/material/material.module';

import { DashboardComponent } from './dashboard.component';
import { dashBoardRoutes } from './dashboard.routing';

@NgModule({
  declarations: [
    DashboardComponent,
    WelcomeCardComponent,
    AudioListCardComponent,
    VideoListCardComponent,
    TotalStorageSpaceComponent,
    UserListCardComponent,
    TbdListCardComponent,
    LoggedUsersListCardComponent,
  ],
  imports: [
    RouterModule.forChild(dashBoardRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    // FlexLayoutModule,
    FeatherModule.pick(allIcons),
    NgxDatatableModule,
    NgApexchartsModule,
    SpinnerModule,
  ],
})
export class DashboardModule {}
