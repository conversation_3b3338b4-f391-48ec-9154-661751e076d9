.leftsidebar:not(.expanded) {
  .menu-label {
    display: none;
  }

  .menu-list-item {
    justify-content: center;
  }
}
.settings-item {
  margin-top: auto;
  padding: 12px;
}

.settings-item .menu-list-item {
  background: #00c2c7;
  color: #fff;
}
.leftsidebar {
  width: 80px;
  background: #ffffff;
  border-right: 1px solid #eee;
  transition: width 0.3s ease;
  overflow: hidden;
}

/* HOVER EXPAND */
.leftsidebar:hover {
  width: 260px;
}
.leftsidebar {
  width: 80px;
  background: #ffffff;
  border-right: 1px solid #eee;
  overflow: visible; /* REQUIRED */
}

/* FULL HEIGHT HOVER AREA */
.sidebar-hover-zone {
  width: 80px;
  height: 100%;
  transition: width 0.3s ease;
  overflow: hidden;
}

/* EXPAND ON HOVER (DESKTOP ONLY) */
@media (min-width: 769px) {
  .sidebar-hover-zone:hover {
    width: 260px;
  }
}
.leftsidebar {
  width: 80px;
  transition: width 0.3s ease;
  overflow: visible;
}

.leftsidebar.sidebar-expanded {
  width: 260px;
}

.menu-list-item {
  display: flex;
  align-items: center;
}

.menu-label {
  display: flex;
  align-items: center;
  width: 100%;
}

.flex-grow {
  flex: 1 1 auto; /* replaces fxFlex */
}

.routeIcon {
  margin-right: 8px;
}

.nav-cap {
  display: block;
}
