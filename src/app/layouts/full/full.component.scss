/* Sidebar Container */
.leftsidebar {
  width: 80px;
  background: #ffffff;
  border-right: 1px solid #eee;
  transition: width 0.3s ease;
  overflow: visible;
  position: relative;
  z-index: 1000;
}

/* Hover Expansion */
.leftsidebar:hover {
  width: 260px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Settings Item at Bottom */
.settings-item {
  margin-top: auto;
  padding: 12px;
}

.settings-item .menu-list-item {
  background: #00c2c7;
  color: #fff;
}

/* Menu Item Layout */
.menu-list-item {
  display: flex;
  align-items: center;
}

.menu-label {
  display: flex;
  align-items: center;
  width: 100%;
}

.flex-grow {
  flex: 1 1 auto;
}

.routeIcon {
  margin-right: 8px;
}

.nav-cap {
  display: block;
}
