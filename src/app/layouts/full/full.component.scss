/* Sidebar Container */
.leftsidebar {
  width: 80px;
  background: #ffffff;
  border-right: 1px solid #eee;
  transition: width 0.3s ease;
  overflow: visible;
  position: relative;
  z-index: 1000;
}

/* Hover Expansion */
.leftsidebar:hover {
  width: 260px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Sidebar Content Styles */
.leftsidebar {
  .vsidebar {
    height: 100%;
    padding: 0;

    .logo-container {
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #eee;
      transition: all 0.3s ease;
    }

    .vmenu-list-item {
      padding: 8px;
    }

    .menu-list-item {
      height: 44px;
      display: flex;
      align-items: center;
      border-radius: 8px;
      margin-bottom: 6px;
      color: #5f6368;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      justify-content: center;
      padding: 0 12px;
      text-decoration: none;

      &:hover {
        background: #f5f7f9;
      }

      &.activeMenu {
        background: #00c2c7 !important;
        color: #ffffff !important;

        .routeIcon {
          color: #ffffff !important;
        }

        .menu-label {
          color: #ffffff !important;
        }
      }
    }

    .routeIcon {
      font-size: 20px;
      min-width: 20px;
      width: 20px;
      height: 20px;
      flex-shrink: 0;
      transition: all 0.3s ease;
    }

    .menu-label {
      opacity: 0;
      white-space: nowrap;
      transition: opacity 0.3s ease 0.2s, transform 0.3s ease 0.2s;
      overflow: hidden;
      flex: 1;
      margin-left: 14px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      transform: translateX(-10px);
    }

    .nav-cap {
      opacity: 0;
      transition: opacity 0.3s ease 0.2s, transform 0.3s ease 0.2s;
      font-size: 12px;
      color: #9e9e9e;
      text-transform: uppercase;
      font-weight: 600;
      margin: 16px 0 8px 0;
      padding: 0 16px;
      white-space: nowrap;
      transform: translateX(-10px);
    }
  }
}

/* Show text and align left when sidebar is hovered */
.leftsidebar:hover {
  .vsidebar {
    .logo-container {
      justify-content: flex-start;
      padding-left: 16px;
    }

    .menu-list-item {
      justify-content: flex-start;
    }

    .menu-label {
      opacity: 1;
      transform: translateX(0);
    }

    .nav-cap {
      opacity: 1;
      transform: translateX(0);
    }
  }
}

/* Settings Item at Bottom */
.settings-item {
  margin-top: auto;
  padding: 12px;
}

.settings-item .menu-list-item {
  background: #00c2c7;
  color: #fff;
}

/* Menu Item Layout */
.menu-list-item {
  display: flex;
  align-items: center;
}

.menu-label {
  display: flex;
  align-items: center;
  width: 100%;
}

.flex-grow {
  flex: 1 1 auto;
}

.routeIcon {
  margin-right: 8px;
}

.nav-cap {
  display: block;
}
