import { Component, Input, OnInit } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { Router } from '@angular/router';
import { AdminService } from 'src/app/services/admin.service';
import { AuthService } from 'src/app/services/auth.service';
import { ProfileService } from 'src/app/services/profile.service';
import { IAdminNotification } from 'src/app/types/interfaces/admin-notifications';
import { IUserCollection } from 'src/app/types/interfaces/user.interface';

import { Messages } from '../../data/messages';
import { Profile } from '../../data/profile';

@Component({
  selector: 'app-vertical-header',
  templateUrl: './vertical-header.component.html',
  styleUrls: ['./vertical-header.component.scss'],
  standalone: false,
})
export class VerticalHeaderComponent implements OnInit {
  @Input() sidebartoggle: MatSidenav | any;

  public showSearch = false;
  public messages: any[] = Messages;
  public profiles: any[] = Profile;
  userId: string = '';
  adminDetails!: IUserCollection;
  adminName!: string;
  notifications: IAdminNotification[] = [];
  notificationsCount: number = 0;
  newNotification: boolean = false;
  selectedMessage!: string;

  constructor(
    private profileService: ProfileService,
    private adminService: AdminService,
    public authService: AuthService,
    private router: Router,
  ) {
    this.userId = this.adminService.getAdminId() || '';
  }

  ngOnInit(): void {
    this.adminDetail();

    this.profileService.profileTrigger.subscribe((msg) => {
      this.selectedMessage = msg;
      if (this.selectedMessage) {
        this.adminDetail();
      }
    });
  }

  adminDetail() {
    this.profileService.getUserById(this.userId).subscribe((res: Object) => {
      if (!res) {
        return;
      }

      this.adminDetails = <IUserCollection>res;
      this.adminName = this.adminDetails.name;
    });

    this.getAdminNotification();
  }

  logout() {
    this.authService.signOut();
    this.router.navigate(['login']);
  }

  getAdminNotification() {
    this.adminService.getAdminNotifications().subscribe((res) => {
      if (!res.status || !res.data) {
        this.newNotification = false;
        return;
      }

      this.newNotification = true;
      this.notifications = res.data;
      this.notificationsCount = res.data.length;
    });
  }

  readNotification(data: IAdminNotification) {
    data.markAsRead = true;
    this.adminService.readAdminNotification(data._id, data.markAsRead).subscribe((res) => {
      if (!res.status) {
        return;
      }

      this.notifications = [];
      this.notificationsCount = this.notificationsCount - 1;
      this.getAdminNotification();
      this.router.navigate(['/video']);
    });
  }

  clearNotification() {
    const markAsRead = true;
    this.adminService.clearAdminNotification(markAsRead).subscribe((res) => {
      if (!res.status) {
        this.newNotification = true;
        return;
      }

      this.notifications = [];
      this.newNotification = false;
    });
  }
}
