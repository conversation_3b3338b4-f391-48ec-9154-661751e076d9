<mat-toolbar class="topbar-toolbar">

  <!-- ============================================================= -->
  <!-- Sidebar Toggle -->
  <!-- ============================================================= -->
  <button
    mat-icon-button
    type="button"
    (click)="sidebartoggle.toggle()"
    aria-label="Toggle sidebar"
  >
    <i-feather class="feather-20 text-light" name="menu"></i-feather>
  </button>

  <!-- ============================================================= -->
  <!-- Search Toggle -->
  <!-- ============================================================= -->
  <!-- <button
    mat-icon-button
    type="button"
    class="srh-btn"
    (click)="showSearch = !showSearch"
    aria-label="Search"
  >
    <i-feather class="feather-20 text-light" name="search"></i-feather>
  </button> -->

  <!-- Search Input -->
  <div *ngIf="showSearch" class="topsearch">
    <mat-form-field
      appearance="fill"
      color="accent"
      class="search-field"
    >
      <input matInput placeholder="Search" />
    </mat-form-field>

    <mat-icon
      class="cursor-pointer"
      (click)="showSearch = false"
    >
      close
    </mat-icon>
  </div>

  <!-- ============================================================= -->
  <!-- FLEX SPACER (replaces fxFlex) -->
  <!-- ============================================================= -->
  <span class="flex-spacer"></span>

  <!-- ============================================================= -->
  <!-- Notifications -->
  <!-- ============================================================= -->
  <button
    mat-icon-button
    [matMenuTriggerFor]="notification"
    class="position-relative"
    aria-label="Notifications"
  >
    <i-feather class="feather-20 text-light" name="bell"></i-feather>
    <span *ngIf="newNotification" class="notify-dot"></span>
  </button>

  <mat-menu #notification="matMenu" class="topbardd notification">
    <mat-action-list>
      <h4 class="ddheadtitle">
        Notifications
        <mat-chip-listbox *ngIf="newNotification">
          <mat-chip color="accent" selected>
            {{ notificationsCount }} new
          </mat-chip>
          <mat-chip color="primary" selected (click)="clearNotification()">
            clear
          </mat-chip>
        </mat-chip-listbox>
      </h4>

      <mat-list-item
        *ngFor="let notification of notifications; last as last"
        (click)="readNotification(notification)"
      >
        <i-feather
          matListIcon
          name="alert-triangle"
          class="text-danger feather-32"
        ></i-feather>

        <h6 matLine>{{ notification.title }}</h6>
        <p matLine class="text-muted">
          {{ notification.content }}
        </p>

        <mat-divider *ngIf="!last"></mat-divider>
      </mat-list-item>
    </mat-action-list>
  </mat-menu>

  <!-- ============================================================= -->
  <!-- Profile -->
  <!-- ============================================================= -->
  <button
    mat-icon-button
    [matMenuTriggerFor]="profile"
    aria-label="Profile"
  >
    <i-feather name="settings" class="text-light"></i-feather>
  </button>

  <mat-menu #profile="matMenu" class="topbardd profile-menu">

    <!-- Title -->
    <h4 class="ddheadtitle m-0 m-b-5 menu-title">User Profile</h4>
  
    <!-- Profile Info -->
    <div class="profile-info">
      <div class="profile-name text-muted">
        {{ adminName | titlecase }}
      </div>
      <div class="profile-role text-muted fw-normal">
        Administrator
      </div>
    </div>
  
    <mat-divider></mat-divider>
  
    <!-- My Profile -->
    <button
      mat-menu-item
      routerLink="/profile"
      class="profile-item"
    >
      <div class="item-text">
        <div class="item-title">My Profile</div>
        <div class="item-subtitle">Account Settings</div>
      </div>
    </button>
  
    <!-- Logout Button -->
    <div class="logout-container">
      <button
        mat-flat-button
        color="accent"
        class="logout-btn"
        (click)="logout()"
      >
        Logout
      </button>
    </div>
  
  </mat-menu>
  

</mat-toolbar>
