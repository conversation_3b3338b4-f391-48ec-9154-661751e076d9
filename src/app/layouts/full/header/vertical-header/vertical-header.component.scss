.topbar-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .flex-spacer {
    flex: 1 1 auto;
  }
  
  /* Search */
  .topsearch {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .search-field {
    width: 220px;
  }
  
  /* Notification dot */
  .notify-dot {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 8px;
    height: 8px;
    background-color: #f44336;
    border-radius: 50%;
  }
  
  /* Dropdown */
  .topbardd {
    min-width: 320px;
    background: #fff !important;
  }
  
  /* Profile */
  .profile-info {
    padding: 16px;
    // text-align: center;
  }
  /* Menu container */
.profile-menu {
  padding: 0;
  min-width: 280px;
}

/* Title */
.menu-title {
  font-size: 18px;
  font-weight: 500;
  padding: 16px 0px 8px;
}

/* Profile section */
.profile-info {
  padding: 8px 20px 16px;
}

.profile-name {
  font-size: 15px;
}

.profile-role {
  font-size: 13px;
  color: #8a8f98;
}

/* Profile item */
.profile-item {
  padding: 12px 20px;
  height: auto;
}

.item-text {
  display: flex;
  flex-direction: column;
}

.item-title {
  font-size: 15px;
  font-weight: 500;
}

.item-subtitle {
  font-size: 14px;
  color: #8a8f98;
}

/* Logout */
.logout-container {
  padding: 16px 20px;
}

.logout-btn {
  width: 100%;
  height: 40px;
  font-weight: 500;
  border-radius: 6px;
  background-color: #03c9d7 !important;
}
.ddtitle{
  font-size: 15px;
}