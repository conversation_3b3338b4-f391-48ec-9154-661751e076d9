import { Component, Input, OnInit } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { Router } from '@angular/router';
import { AdminService } from 'src/app/services/admin.service';
import { IAdminNotification } from 'src/app/types/interfaces/admin-notifications';

import { Messages } from '../../data/messages';
import { Profile } from '../../data/profile';

@Component({
  selector: 'app-horizontal-header',
  templateUrl: './horizontal-header.component.html',
  styleUrls: [],
  standalone: false,
})
export class HorizontalHeaderComponent implements OnInit {
  @Input() sidebartoggle: MatSidenav | any;

  public showSearch = false;
  public messages: any[] = Messages;
  public profiles: any[] = Profile;

  notifications: IAdminNotification[] = [];
  notificationsCount: number = 0;
  newNotification: boolean = false;

  constructor(private adminService: AdminService, private router: Router) {}

  ngOnInit(): void {
    this.getAdminNotification();
  }

  getAdminNotification() {
    this.adminService.getAdminNotifications().subscribe((res) => {
      if (!res.status || !res.data) {
        this.newNotification = false;
        return;
      }
      this.newNotification = true;
      this.notifications = res.data;
      this.notificationsCount = res.data.length;
    });
  }
  readNotification(data: IAdminNotification) {
    data.markAsRead = true;
    this.adminService.readAdminNotification(data._id, data.markAsRead).subscribe((res) => {
      if (!res.status) {
        return;
      }
      this.notifications = [];
      this.notificationsCount = this.notificationsCount - 1;
      this.getAdminNotification();
      this.router.navigate(['/video']);
    });
  }
}
