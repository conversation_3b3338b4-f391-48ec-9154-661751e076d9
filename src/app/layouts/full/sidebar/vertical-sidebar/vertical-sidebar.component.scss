/* Vertical Sidebar Container */
.vsidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Logo Container */
.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #eee;
  transition: all 0.3s ease;
}

/* Logo alignment when sidebar expands */
:host-context(.leftsidebar:hover) .logo-container {
  justify-content: flex-start;
  padding-left: 16px;
}

/* Menu List Container */
.vmenu-list-item {
  padding: 8px;
  flex: 1;
}

/* Material List Item Styling */
.mat-mdc-list-item {
  border-radius: 8px;
  margin-bottom: 6px;
}
