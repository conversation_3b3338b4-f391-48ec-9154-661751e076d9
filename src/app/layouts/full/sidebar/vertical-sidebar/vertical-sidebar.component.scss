/* left sidebar */
.leftsidebar {
  width: 80px;           /* collapsed */
  background: #ffffff;
  border-right: 1px solid #f0f0f0;
}

/* expanded sidebar */
.leftsidebar.expanded {
  width: 260px;
}
.vsidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f1f1f1;
}
.vmenu-list-item {
  padding: 8px;
}

.mat-mdc-list-item {
  border-radius: 8px;
  margin-bottom: 6px;
}
.vsidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #eee;
}

/* Center logo when collapsed */
.leftsidebar:hover .logo-container {
  justify-content: flex-start;
  padding-left: 16px;
}
.vsidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}
