import { NavItem } from '../vertical-sidebar/menu-list-item/nav-item';

export const navItems: NavItem[] = [
  {
    displayName: 'Dashboard',
    iconName: 'Monitor',
    route: '/dashboard',
  },
  {
    displayName: 'Video',
    iconName: 'video',
    route: '/video',
  },
  {
    displayName: 'Audio',
    iconName: 'headphones',
    route: '/audio',
  },
  {
    displayName: 'Article',
    iconName: 'File',
    route: '/article',
  },
  {
    displayName: 'Event',
    iconName: 'calendar',
    route: '/events',
  },
  {
    displayName: 'Message Board',
    iconName: 'message-square',
    route: '/messages',
  },
  {
    displayName: 'Notification',
    iconName: 'bell',
    route: '/notification',
  },
  {
    displayName: 'Category',
    iconName: 'tv',
    route: '/category',
  },
  {
    displayName: 'Manage users',
    iconName: 'users',
    route: '/manage-users',
  },
  {
    displayName: 'Manage Admins',
    iconName: 'key',
    route: '/manage-Admins',
  },
  {
    displayName: 'Profile',
    iconName: 'user',
    route: '/profile',
  },
  {
    displayName: 'Settings',
    iconName: 'Settings',
    route: '/settings',
  },
];
