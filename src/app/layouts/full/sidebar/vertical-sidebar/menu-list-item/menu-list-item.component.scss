/* Component-specific styles - Global styles handle the main behavior */
.menu-list-item {
  text-decoration: none;
  color: inherit;
}

/* Active Menu State */
.activeMenu {
  background: #00c2c7 !important;
  color: #ffffff !important;
}

.activeMenu .routeIcon {
  color: #ffffff !important;
}

.activeMenu .menu-label {
  color: #ffffff !important;
}

/* Expand/Collapse Icon */
mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  line-height: 18px;
}
