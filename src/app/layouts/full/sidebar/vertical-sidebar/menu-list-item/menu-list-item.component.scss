.menu-list-item {
  height: 44px;
  display: flex;
  align-items: center;
  gap: 14px;
  color: #5f6368;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* icon */
.routeIcon {
  font-size: 20px;
  min-width: 20px;
}

/* label text */
.menu-label {
  white-space: nowrap;
}

/* hover */
.menu-list-item:hover {
  background: #f5f7f9;
}

/* active menu */
.activeMenu {
  background: #00c2c7;        /* teal like image */
  color: #ffffff;
}

.activeMenu .routeIcon {
  color: #ffffff;
}
.menu-item {
  height: 44px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 14px;
  margin-bottom: 6px;
  color: #5f6368;
  font-weight: 500;
  transition: background 0.2s ease;
}

/* ICON */
.menu-icon {
  font-size: 20px;
  min-width: 20px;
}

/* LABEL (hidden by default) */
.menu-label {
  opacity: 0;
  white-space: nowrap;
  transition: opacity 0.2s ease;
}

/* SHOW TEXT ON SIDEBAR HOVER */
.leftsidebar:hover .menu-label {
  opacity: 1;
}

/* CENTER ICON WHEN COLLAPSED */
.menu-item {
  justify-content: center;
}

/* ALIGN LEFT WHEN EXPANDED */
.leftsidebar:hover .menu-item {
  justify-content: flex-start;
}

/* HOVER */
.menu-item:hover {
  background: #f2f4f7;
}

/* ACTIVE */
.menu-item.active {
  background: #00c2c7;
  color: #ffffff;
}

.menu-item.active .menu-icon {
  color: #ffffff;
}

.menu-label {
  transition: opacity 0.15s ease 0.1s;
}
.menu-list-item {
  height: 44px;
  display: flex;
  align-items: center;
  gap: 14px;
  border-radius: 8px;
  margin-bottom: 6px;
  color: #5f6368;
  font-weight: 500;
  transition: background 0.2s ease;
}

/* ICON */
.routeIcon {
  font-size: 20px;
  min-width: 20px;
}

/* TEXT HIDDEN BY DEFAULT */
.menu-label {
  opacity: 0;
  white-space: nowrap;
  transition: opacity 0.2s ease;
}

/* SHOW TEXT ON SIDEBAR HOVER */
.sidebar-hover-zone:hover .menu-label {
  opacity: 1;
}

/* CENTER ICON WHEN COLLAPSED */
.menu-list-item {
  justify-content: center;
}

/* ALIGN LEFT WHEN EXPANDED */
.sidebar-hover-zone:hover .menu-list-item {
  justify-content: flex-start;
}

/* HOVER */
.menu-list-item:hover {
  background: #f2f4f7;
}

/* ACTIVE */
.activeMenu {
  background: #00c2c7;
  color: #ffffff;
}

.activeMenu .routeIcon {
  color: #ffffff;
}
.menu-label {
  opacity: 0;
  white-space: nowrap;
  transition: opacity 0.2s ease;
}

/* SHOW TEXT WHEN SIDEBAR HAS CLASS */
:host-context(.sidebar-expanded) .menu-label {
  opacity: 1;
}

/* CENTER ICON WHEN COLLAPSED */
.menu-list-item {
  justify-content: center;
}

/* LEFT ALIGN WHEN EXPANDED */
:host-context(.sidebar-expanded) .menu-list-item {
  justify-content: flex-start;
}
