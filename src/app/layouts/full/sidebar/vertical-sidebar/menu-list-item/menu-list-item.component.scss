/* Menu List Item Base Styles */
.menu-list-item {
  height: 44px;
  display: flex;
  align-items: center;
  border-radius: 8px;
  margin-bottom: 6px;
  color: #5f6368;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  justify-content: center; /* Center by default (collapsed state) */
  padding: 0 12px;
  position: relative;
  text-decoration: none;
}

/* Icon Styles */
.routeIcon {
  font-size: 20px;
  min-width: 20px;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Menu Label - Hidden by default */
.menu-label {
  opacity: 0;
  white-space: nowrap;
  transition: opacity 0.3s ease 0.2s;
  overflow: hidden;
  flex: 1;
  margin-left: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transform: translateX(-10px);
  transition: opacity 0.3s ease 0.2s, transform 0.3s ease 0.2s;
}

/* Show text and align left when sidebar is hovered */
:host-context(.leftsidebar:hover) .menu-list-item {
  justify-content: flex-start;
  gap: 0;
}

:host-context(.leftsidebar:hover) .menu-label {
  opacity: 1;
  transform: translateX(0);
}

/* Hover Effects */
.menu-list-item:hover {
  background: #f5f7f9;
}

/* Active Menu State */
.activeMenu {
  background: #00c2c7 !important;
  color: #ffffff !important;
}

.activeMenu .routeIcon {
  color: #ffffff !important;
}

.activeMenu .menu-label {
  color: #ffffff !important;
}

/* Navigation Caption Styles */
.nav-cap {
  opacity: 0;
  transition: opacity 0.3s ease 0.2s, transform 0.3s ease 0.2s;
  font-size: 12px;
  color: #9e9e9e;
  text-transform: uppercase;
  font-weight: 600;
  margin: 16px 0 8px 0;
  padding: 0 16px;
  white-space: nowrap;
  transform: translateX(-10px);
}

:host-context(.leftsidebar:hover) .nav-cap {
  opacity: 1;
  transform: translateX(0);
}

/* Expand/Collapse Icon */
mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  line-height: 18px;
}
