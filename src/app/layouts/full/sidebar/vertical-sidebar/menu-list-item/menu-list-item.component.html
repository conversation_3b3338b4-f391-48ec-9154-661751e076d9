<!-- Navigation Caption -->
<small class="nav-cap" *ngIf="item.navCap">
  {{ item.navCap }}
</small>

<!-- Menu Item -->
<a
  mat-list-item
  class="menu-list-item"
  [ngStyle]="{ 'padding-left': depth * 9 + 'px' }"
  (click)="onItemSelected(item)"
  [ngClass]="{
    activeMenu: item.route ? router.isActive(item.route, true) : false,
    expanded: expanded
  }"
  *ngIf="!item.navCap"
  [title]="item.displayName"
>
  <!-- Icon -->
  <i-feather class="routeIcon" name="{{ item.iconName }}"></i-feather>

  <!-- Menu Label (text) -->
  <span class="menu-label" *ngIf="!item.children?.length">
    {{ item.displayName }}
  </span>

  <!-- Menu Label with Expand Arrow (for items with children) -->
  <span class="menu-label" *ngIf="item.children?.length">
    {{ item.displayName }}
    <mat-icon [@indicatorRotate]="expanded ? 'expanded' : 'collapsed'" style="margin-left: auto;">
      expand_more
    </mat-icon>
  </span>
</a>

<!-- Submenu Items -->
<div *ngIf="expanded && item.children?.length">
  <app-menu-list-item
    *ngFor="let child of item.children"
    [item]="child"
    [depth]="depth + 1">
  </app-menu-list-item>
</div>
