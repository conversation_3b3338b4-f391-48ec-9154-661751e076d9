<small class="p-15 mat-small nav-cap" *ngIf="item.navCap">
  {{ item.navCap }}
</small>

<a
  mat-list-item
  class="menu-list-item"
  [ngStyle]="{ 'padding-left': depth * 9 + 'px' }"
  (click)="onItemSelected(item)"
  [ngClass]="{
    activeMenu: item.route ? router.isActive(item.route, true) : false,
    expanded: expanded
  }"
  *ngIf="!item.navCap"
  [title]="item.displayName"
>
  <i-feather class="routeIcon" name="{{ item.iconName }}"></i-feather>
  {{ item.children?.length }}
  <span class="menu-label flex-grow" *ngIf="item.children?.length">
    <span class="flex-grow"></span>
    {{ item.displayName }}
    <mat-icon [@indicatorRotate]="expanded ? 'expanded' : 'collapsed'">
      expand_more
    </mat-icon>
  </span>
</a>

<div *ngIf="expanded">
  <app-menu-list-item
    *ngFor="let child of item.children"
    [item]="child"
    [depth]="depth + 1">
  </app-menu-list-item>
</div>
