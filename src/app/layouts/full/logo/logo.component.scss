.brand {
  line-height: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  padding: 0;
}

.logo-img {
  height: 40px;
  width: 40px;
  border-radius: 50%;
  object-fit: cover;
  transition: all 0.3s ease;
}

/* Compact logo styles (default collapsed state) */
.compact-logo {
  .brand {
    padding: 0 !important;
    margin: 0;
    justify-content: center;
  }

  .logo-img {
    height: 32px;
    width: 32px;
  }
}

/* Logo alignment when sidebar is hovered/expanded */
:host-context(.leftsidebar:hover) .compact-logo .brand {
  justify-content: flex-start;
  padding-left: 16px !important;
}
