import { Component, OnInit } from '@angular/core';
import { CustomizerService } from 'src/app/services/customizer.service';
import { DashboardService } from 'src/app/services/dashboard.service';
import { IChurchCollection } from 'src/app/types/interfaces/church.interface';

@Component({
  selector: 'app-logo',
  templateUrl: './logo.component.html',
  styleUrls: ['./logo.component.scss'],
  standalone: false,
})
export class LogoComponent implements OnInit {
  adminDashboard!: IChurchCollection;
  isLoading: boolean = false;
  constructor(private dashboardService: DashboardService, public customizer: CustomizerService) {}

  ngOnInit(): void {
    this.getChurchAdminDetails();
  }

  getChurchAdminDetails() {
    this.isLoading = true;
    this.dashboardService.getChurchAdminDetails().subscribe((res) => {
      if (!res.status || !res.data) {
        this.isLoading = false;

        return;
      }
      this.isLoading = false;

      this.adminDashboard = res.data[0];
    });
  }
}
