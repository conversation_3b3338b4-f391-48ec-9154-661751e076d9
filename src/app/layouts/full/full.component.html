<!-- ============================================================== -->
<!-- Main wrapper -->
<!-- ============================================================== -->
<div class="main-container">
  <mat-sidenav-container class="sidenav-container">
    <!-- ============================================================== -->
    <!-- Sidebar -->
    <!-- ============================================================== -->
  <mat-sidenav
  [opened]="true"
  #sidebar
  mode="side"
  class="leftsidebar">

  <div class="sidebar-hover-zone">
    <app-vertical-sidebar></app-vertical-sidebar>
  </div>

</mat-sidenav>

    <!-- ============================================================== -->
    <!-- Customizer Sidebar -->
    <!-- ============================================================== -->
    <mat-sidenav
      opened="false"
      #rightsidebar
      position="end"
      mode="over"
      class="customizerSidebar"
    >
      <app-customizer></app-customizer>
    </mat-sidenav>

    <!-- ============================================================== -->
    <!-- Page Container -->
    <!-- ============================================================== -->
    <mat-sidenav-content class="page-wrapper">
      <!-- Topbar -->
      <div *ngIf="!customizer.horizontal; else horizontalheader" class="topbar">
        <mat-toolbar>
          <app-vertical-header
            class="w-100"
            [sidebartoggle]="sidebar"
          ></app-vertical-header>
        </mat-toolbar>
      </div>

      <ng-template #horizontalheader>
        <div class="topbar">
          <mat-toolbar>
            <app-horizontal-header
              [sidebartoggle]="sidebar"
              class="w-100 horizontal-container align-items-center"
            ></app-horizontal-header>
          </mat-toolbar>
        </div>
      </ng-template>

      <!-- Page content -->
      <div class="page-content">
        <app-horizontal-sidebar
          *ngIf="customizer.horizontal && mobileQuery.matches"
        ></app-horizontal-sidebar>

        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>
