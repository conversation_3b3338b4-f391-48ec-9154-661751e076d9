<div *ngIf="loadingInfo" class="flex-center">
  <app-spinner></app-spinner>
</div>
<div
  *ngIf="info"
  [dir]="this.customizer.dir"
  class="mainboxContainer"
  [ngClass]="{
    horizontal: this.customizer.horizontal,
    fixedTopbar: this.customizer.fixedTopbar,
    minisidebar: this.customizer.minisidebar,
    blueTheme: this.customizer.currentTheme === 'blueTheme',
    orangeTheme: this.customizer.currentTheme === 'orangeTheme',
    greenTheme: this.customizer.currentTheme === 'greenTheme',
    redTheme: this.customizer.currentTheme === 'redTheme',
    purpleTheme: this.customizer.currentTheme === 'purpleTheme',
    indigoTheme: this.customizer.currentTheme === 'indigoTheme'
  }">
  <router-outlet></router-outlet>
</div>
<div *ngIf="!info && !loadingInfo"><app-invalid-domain></app-invalid-domain></div>
