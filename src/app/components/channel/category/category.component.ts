import { DatePipe } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { CategoryService } from 'src/app/services/category.service';
import { ICategoryCollection } from 'src/app/types/interfaces/category.interface';

import { DialogComponent } from '../dialog/dialog.component';

@Component({
  selector: 'app-category',
  templateUrl: './category.component.html',
  styleUrls: ['./category.component.scss'],
  standalone: false,
})
export class CategoryComponent implements OnInit, AfterViewInit {
  category: ICategoryCollection[] = [];

  totalRows = 0;
  pageSize = 25;
  currentPage = 0;

  msg = '';
  searchText = '';
  isLoading: boolean = false;
  noMedia: boolean = false;

  @ViewChild(MatTable, { static: true }) table: MatTable<ICategoryCollection> = Object.create(null);
  displayedColumns: string[] = ['#', 'name', 'type', 'updatedAt', 'action'];
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator = Object.create(null);

  constructor(public dialog: MatDialog, public datePipe: DatePipe, private categoryService: CategoryService) {}

  ngOnInit(): void {
    this.getCategories(this.currentPage, this.pageSize, this.searchText);
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
  }

  openDialog(action: string, data: object): void {
    let obj = { ...data, action };
    const dialogRef = this.dialog.open(DialogComponent, { data: obj });
    dialogRef.afterClosed().subscribe((result) => {
      this.getCategories(this.currentPage, this.pageSize, this.searchText);
    });
  }

  getCategories(page: number, pageSize: number, search: string) {
    try {
      this.isLoading = true;
      this.categoryService.getAllCategories(page, pageSize, search).subscribe({
        next: (res) => {
          if (!res.status || !res.data) {
            this.isLoading = false;
            this.noMedia = true;
            this.msg = res.message;
            return;
          }

          this.noMedia = false;
          this.isLoading = false;
          this.dataSource.data = res.data.result;

          setTimeout(() => {
            this.paginator.pageIndex = this.currentPage;
            this.paginator.length = res.data?.total ?? 0;
          });
        },
        error: (err) => {
          this.isLoading = false;
          this.noMedia = true;
          this.msg = err.error.message;
          return 'error';
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  pageChanged(event: PageEvent) {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.getCategories(this.currentPage, this.pageSize, this.searchText);
  }

  searchCategory() {
    this.getCategories(this.currentPage, this.pageSize, this.searchText);
    this.paginator.firstPage();
  }

  clearSearch() {
    this.searchText = '';
    this.getCategories(this.currentPage, this.pageSize, this.searchText);
  }
}
