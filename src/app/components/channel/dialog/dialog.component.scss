.upload-image {
  width: 150px;
  height: 150px;
}

.clear-image {
  padding: 0px !important;
  color: red;
}
/* Flex utilities */
.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.align-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

/* Flex sizing */
.flex-100 {
  flex: 1 1 100%;
}

/* Spacing helpers */
.mt-5 {
  margin-top: 5px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-15 {
  margin-bottom: 15px;
}

.ml-15 {
  margin-left: 15px;
}

.mr-15 {
  margin-right: 15px;
}

/* Image upload styles */
.upload-image {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #ccc;
}

.clear-image {
  margin-top: 10px;
  color: #f44336;
}

.image-informative-cls {
  display: block;
  font-size: 13px;
  color: #666;
}

.input-file-button label {
  cursor: pointer;
}

.spinner {
  opacity: 0.6;
  pointer-events: none;
}
