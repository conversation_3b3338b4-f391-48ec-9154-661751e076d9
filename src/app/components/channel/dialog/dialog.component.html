<h2 class="font-medium" mat-dialog-title>
  <strong>{{ action }}</strong>
</h2>

<div class="pb-3" *ngIf="action === 'Add Category' || action === 'Update Category'; else elseTemplate">
  <form #categoryForm="ngForm">
    <mat-dialog-content>
      <!-- Image Upload Section -->
      <div class="d-flex align-center mb-15">
        <div class="d-flex flex-column">
          <img
            class="upload-image"
            [src]="localData.thumbnail"
            onerror="this.src='assets/images/backgrounds/thumbnail-dummy-image.svg'" />

          <button
            class="clear-image"
            mat-button
            *ngIf="imageUpload || localData.thumbnail"
            matSuffix
            mat-raised-button
            aria-label="Clear"
            (click)="clearUploads()">
            <mat-icon>highlight_off</mat-icon>Clear
          </button>
        </div>

        <div class="ml-15">
          <div class="d-flex mt-5">
            <button mat-raised-button color="primary" class="ml-15 input-file-button">
              <label for="images">Select thumbnail</label>
              <input
                type="file"
                id="images"
                accept="image/png, image/jpeg, image/jpg"
                required
                (change)="selectImage($event)" />
            </button>
          </div>
          <label class="image-informative-cls mt-10">Acceptable type: png, jpg, jpeg</label>
          <label class="image-informative-cls">Upload limit: 15MB</label>
          <label class="image-informative-cls">Width: 150px Height: 150px</label>
        </div>
      </div>

      <!-- Category Name -->
      <div class="d-flex flex-wrap align-center">
        <div class="flex-100">
          <div class="mr-15 ml-15">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="name"
                name="name"
                [(ngModel)]="localData.name"
                placeholder="Name"
                #input
                maxlength="50" />
              <mat-hint align="end">{{ input.value.length || 0 }}/50</mat-hint>
            </mat-form-field>
          </div>
        </div>
      </div>

      <!-- Category Type -->
      <div class="d-flex flex-wrap align-center">
        <div class="flex-100">
          <div class="mr-15 ml-15">
            <mat-form-field *ngIf="action === 'Add Category'; else update">
              <mat-select name="channelId" [(ngModel)]="localData.type" placeholder="Category type" required>
                <mat-option value="video">Video</mat-option>
                <mat-option value="audio">Audio</mat-option>
              </mat-select>
            </mat-form-field>

            <ng-template #update>
              <mat-form-field>
                <mat-select
                  disabled="true"
                  name="channelId"
                  [(ngModel)]="localData.type"
                  placeholder="Category type"
                  required>
                  <mat-option value="video">Video</mat-option>
                  <mat-option value="audio">Audio</mat-option>
                </mat-select>
              </mat-form-field>
            </ng-template>
          </div>
        </div>
      </div>
    </mat-dialog-content>

    <!-- Actions -->
    <mat-dialog-actions>
      <button
        mat-button
        (click)="doAction()"
        mat-flat-button
        color="primary"
        [class.spinner]="loading"
        [disabled]="!categoryForm.valid || loading">
        {{ action }}
      </button>
      <button mat-button (click)="closeDialog()">Cancel</button>
    </mat-dialog-actions>
  </form>
</div>

<!-- Delete / Confirmation Template -->
<ng-template #elseTemplate>
  <p>
    Sure to {{ action }} <b>{{ localData.name }}</b
    >?
  </p>
  <div mat-dialog-actions align="center" class="pt-3">
    <button
      mat-button
      (click)="doAction()"
      mat-flat-button
      color="primary"
      [class.spinner]="loading"
      [disabled]="loading">
      {{ action }}
    </button>
    <button mat-button (click)="closeDialog()">Cancel</button>
  </div>
</ng-template>
