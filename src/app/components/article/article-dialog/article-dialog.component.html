<h2 class="font-medium" mat-dialog-title>
  <strong>{{ action }}</strong>
</h2>

<div class="pb-3" *ngIf="action === 'Add Article' || action === 'Update Article'; else elseTemplate">
  <form #articleForm="ngForm">
    <mat-dialog-content>
      <!-- Upload images -->
      <div class="form-row row">
        <!-- Thumbnail upload -->
        <div class="form-col col">
          <div class="m-r-15 m-l-15">
            <div class="upload-section">
              <div class="upload-image-col">
                <img
                  class="upload-image"
                  [src]="localData.thumbnail ? localData.thumbnail : 'assets/images/logos/logo.png'" />
                <button
                  class="clear-image"
                  *ngIf="thumbnailUpload"
                  matSuffix
                  mat-raised-button
                  aria-label="Clear"
                  (click)="clearUploads('thumbnail')">
                  <mat-icon>highlight_off</mat-icon>Clear
                </button>
              </div>

              <div class="upload-info">
                <button mat-raised-button color="primary" class="input-file-button primary-btn">
                  <label for="images">Select thumbnail</label>
                  <input
                    type="file"
                    id="images"
                    accept="image/png, image/jpeg, image/jpg"
                    required
                    (change)="selectImage($event)" />
                </button>
                <label class="image-informative-cls m-t-10">Acceptable type: png, jpg, jpeg</label>
                <label class="image-informative-cls">Upload limit: 15MB</label>
                <label class="image-informative-cls">Width: 100px Height: 100px</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Article image upload -->
        <div class="form-col col">
          <div class="m-r-15 m-l-15">
            <div class="upload-section">
              <div class="upload-image-col">
                <img
                  class="upload-image"
                  [src]="localData.articleImage"
                  onerror="this.src='assets/images/logos/logo.png'" />
                <button
                  class="clear-image"
                  *ngIf="articleImgUpload"
                  matSuffix
                  mat-raised-button
                  aria-label="Clear"
                  (click)="clearUploads('image')">
                  <mat-icon>highlight_off</mat-icon>Clear
                </button>
              </div>

              <div class="upload-info">
                <button mat-raised-button color="accent" class="input-file-button accent-btn">
                  <label for="media">Select article image</label>
                  <input
                    type="file"
                    id="media"
                    accept="image/png, image/jpeg, image/jpg"
                    required
                    (change)="selectArticleImage($event)" />
                </button>
                <label class="image-informative-cls m-t-10">Acceptable type: png, jpg, jpeg</label>
                <label class="image-informative-cls">Upload limit: 15MB</label>
                <label class="image-informative-cls">Width: 200px Height: 200px</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Title, Author, and Article Content -->
      <div class="form-row">
        <div class="form-col-full">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="title"
                name="title"
                [(ngModel)]="localData.title"
                placeholder="Title"
                #title="ngModel"
                #input
                maxlength="100" />
              <mat-hint align="end">{{ input.value.length || 0 }}/100</mat-hint>
            </mat-form-field>
          </div>
        </div>

        <div class="form-col-full">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="author"
                name="author"
                placeholder="Author"
                [(ngModel)]="localData.author"
                #inputAuthor
                maxlength="26" />
              <mat-hint align="end">{{ inputAuthor.value.length || 0 }}/26</mat-hint>
            </mat-form-field>
          </div>
        </div>

        <div class="form-col-full">
          <div class="m-r-15 m-l-15">
            <mat-card class="m-b-5 m-l-5">
              <mat-card-content>
                <mat-card-subtitle class="m-b-20">Article</mat-card-subtitle>
                <ckeditor
                  [editor]="Editor"
                  [(ngModel)]="localData.articleData"
                  [config]="ckEditorConfig"
                  id="editor"
                  name="editor"
                  required
                ></ckeditor>

              </mat-card-content>
             
            </mat-card>
             <!-- <ckeditor [editor]="Editor"></ckeditor> -->
          </div>
        </div>
      </div>
    </mat-dialog-content>

    <!-- Buttons -->
    <mat-dialog-actions>
      <button
        mat-button
        (click)="publish()"
        mat-flat-button
        color="primary"
        [class.spinner]="loading"
        [disabled]="!articleForm.valid || loading">
        <span *ngIf="action === 'Add Article'">Publish</span>
        <span *ngIf="action === 'Update Article' && !localData.published">Update and publish</span>
        <span *ngIf="action === 'Update Article' && localData.published">Update</span>
      </button>

      <button
        *ngIf="!localData.published || action === 'Add Article'"
        mat-flat-button
        (click)="draft()"
        [class.spinner]="draftLoading"
        [disabled]="!articleForm.valid || draftLoading">
        Save as draft
      </button>

      <button mat-button (click)="closeDialog()">Cancel</button>
    </mat-dialog-actions>
  </form>
</div>

<!-- Delete / Confirm Template -->
<ng-template #elseTemplate>
  <p class="delete-text">
    Sure to {{ action }} <b>{{ localData.title }}</b> ?
  </p>
  <div mat-dialog-actions align="center" class="pt-3">
    <button
      mat-button class="orange-primary"
      (click)="doAction()"
      mat-flat-button
      color="primary"
      [class.spinner]="loading"
      [disabled]="loading">
      {{ action }}
    </button>
    <button mat-button class="text-btn" (click)="closeDialog()">Cancel</button>
  </div>
</ng-template>
