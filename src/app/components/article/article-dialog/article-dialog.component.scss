.cke_bottom {
  display: none !important;
}
.clear-image {
  padding: 0px !important;
  color: red;
}
/* Two-column responsive layout */
.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.form-col {
  flex: 1 1 45%;
  box-sizing: border-box;
}

.form-col-full {
  flex: 1 1 100%;
  box-sizing: border-box;
}

/* Upload section layout */
.upload-section {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.upload-image-col {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-info {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* Image styles */
.upload-image {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #ccc;
}

.clear-image {
  margin-top: 8px;
}

.input-file-button {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  color: #fff;
}

.input-file-button input {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.image-informative-cls {
  font-size: 12px;
  color: #666;
}

.cke_notification_warning {
  display: none !important;
}

/* Responsive */
@media (max-width: 960px) {
  .form-col {
    flex: 1 1 100%;
  }
}
.primary-btn{
  background-color: #fb9778;
}
.accent-btn{
  background-color: #03c9d7;
}
::ng-deep {
.mat-mdc-dialog-surface.mdc-dialog__surface{
  border-radius: 4px;
  background: #fff;
}
.mat-mdc-dialog-container{
  padding: 24px !important;
}
.mat-mdc-card{
  box-shadow: 0 2px 1px -1px #0003, 0 1px 1px #00000024, 0 1px 3px #0000001f;
  background: #fff;
}
}
:host ::ng-deep .ck-editor__editable {
  min-height: 250px;
}

mat-dialog-content {
  max-height: calc(90vh - 150px);
  overflow: auto;
}
.delete-text{
  padding: 0px 24px;
}