/* ===============================
   Loader
================================ */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* ===============================
   Top Search Section
================================ */
.article-header {
  margin-bottom: 24px;
}

.article-header-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 18px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
}

/* Search input */
.article-search-field {
  flex: 1;
  max-width: 320px;
  height: 46px;
}

.article-search-field .search-input{
  border-bottom: 1px solid !important;
}
/* Remove Material border */
.article-search-field .mat-mdc-notched-outline {
  display: none;
}

.article-search-field .mat-mdc-text-field-wrapper {
  background: transparent;
  box-shadow: none;
  height: 56px;
}

.article-search-field .mat-mdc-form-field-flex {
  height: 56px;
  display: flex;
  align-items: center;
}

.article-search-field .mat-mdc-form-field-infix {
  padding: 0;
}

.article-search-field input {
  font-size: 15px;
}

/* Add Article Button */
.add-article-btn {
  // height: 56px;
  padding: 0 24px;
  border-radius: 4px;
  background-color: #12c7c3;
  color: #ffffff;
  font-weight: 500;

  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.add-article-btn mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 20px;
}

/* ===============================
   Article Grid
================================ */
.article-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 20px;
}

/* ===============================
   Article Card
================================ */
.article-card {
  border-radius: 18px;
  overflow: hidden;
}

.card-wrapper {
  display: flex;
  height: 100%;
}

/* Thumbnail */
.thumbnail {
  width: 120px;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Content */
.content {
  flex: 1;
  padding: 16px;
  position: relative;
}

.title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.author {
  margin: 6px 0;
  color: #666;
}

.date {
  font-size: 13px;
  color: #999;
}

.status {
  margin-top: 8px;
  font-size: 14px;
}

.yes {
  color: #2e7d32;
  font-weight: 600;
}

.no {
  color: #c62828;
  font-weight: 600;
}

/* Actions */
.actions {
  position: absolute;
  bottom: 12px;
  right: 12px;
  display: flex;
}

.actions i-feather{
  line-height: 1;
}

/* ===============================
   Pagination
================================ */
.paginator-card {
  margin-top: 20px;
  border-radius: 14px;
}

/* ===============================
   No Data
================================ */
.no-data {
  text-align: center;
  margin-top: 40px;
  color: #888;
}
.article-search-field .mdc-text-field{
  padding: 0px;
}
.article-header-card{
  justify-content: space-between;
}

::ng-deep {
.add-article-btn .mdc-button__label{
    justify-content: center;
    display: flex;
    align-items: center;
}
.mat-mdc-notch-piece.mdc-notched-outline__trailing, .mat-mdc-notch-piece.mdc-notched-outline__notch, .mat-mdc-notch-piece.mdc-notched-outline__leading{
  border: unset !important;
}
.mat-mdc-dialog-surface.mdc-dialog__surface{
  border-radius: 4px;
}
}
