<!-- Loader -->
<div *ngIf="isLoading" class="flex-center">
  <app-spinner></app-spinner>
</div>

<!-- Search + Add Article -->
<div class="article-header">
  <div class="article-header-card">
    <mat-form-field
      appearance="outline"
      class="article-search-field"
      >

       <input
        matInput
        placeholder="Search Article" class="search-input"
        autocomplete="off"
        (keyup.enter)="searchArticleByTitle()"
        [(ngModel)]="searchArticle" />
     
        <button
        *ngIf="searchArticle"
        mat-icon-button
        matSuffix
        (click)="clearSearch()">
        <mat-icon>close</mat-icon>
      </button>
    </mat-form-field>

    <button
      mat-flat-button
      class="add-article-btn"
      (click)="openArticleDialog('Add Article', {})">
      <i-feather name="plus-circle" class="feather-15"></i-feather>
      <span> Add Article</span>
    </button>
  </div>
</div>


<h2 *ngIf="noArticle" class="no-data">{{ msg }}</h2>

<!-- Articles Grid -->
<div class="article-grid">
  <mat-card class="article-card" *ngFor="let article of obs | async">
    <div class="card-wrapper">
      <!-- Thumbnail -->
      <div class="thumbnail">
        <img
          [src]="article.thumbnail"
          (error)="article.thumbnail='assets/images/backgrounds/default-thumbnail.jpeg'"
          alt="thumbnail" />
      </div>

      <!-- Content -->
      <div class="content">
        <h3 class="title" [title]="article.title">
          {{ article.shortTitle | titlecase }}
        </h3>

        <p class="author">{{ article.shortAuthor | titlecase }}</p>
        <p class="date">{{ article.createdAt | date:'MMM d, y' }}</p>

        <p class="status">
          Published:
          <span [class.yes]="article.published" [class.no]="!article.published">
            {{ article.published ? 'YES' : 'NO' }}
          </span>
        </p>

        <!-- Actions -->
        <div class="actions">
          <button mat-icon-button color="primary"
            (click)="openArticleDialog('Update Article', article)">
            <i-feather name="edit" class="text-primary feather-18"></i-feather>
          </button>

          <button mat-icon-button color="warn"
            (click)="openArticleDialog('Delete Article', article)">
            <i-feather name="trash" class="text-danger feather-18"></i-feather>
          </button>
        </div>
      </div>
    </div>
  </mat-card>
</div>

<!-- Pagination -->
<mat-card class="paginator-card">
  <mat-paginator
    [length]="totalRows"
    [pageIndex]="currentPage"
    [pageSize]="pageSize"
    [pageSizeOptions]="[12, 24, 36, 48]"
    (page)="pageChanged($event)">
  </mat-paginator>
</mat-card>
