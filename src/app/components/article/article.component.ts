import { AfterViewInit, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { Observable } from 'rxjs';
import { ArticleService } from 'src/app/services/article.service';
import { IArticleCollection } from 'src/app/types/interfaces/article';

import { ArticleDialogComponent } from './article-dialog/article-dialog.component';

enum Actions {
  ADD_ARTICLE = 'Add Article',
  UPDATE_ARTICLE = 'Update Article',
  DELETE_ARTICLE = 'Delete Article',
}

@Component({
  selector: 'app-article',
  templateUrl: './article.component.html',
  styleUrls: ['./article.component.scss'],
  standalone: false,
})
export class ArticleComponent implements OnInit, AfterViewInit {
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator = Object.create(null);
  dataSource: MatTableDataSource<IArticleCollection> = new MatTableDataSource<IArticleCollection>();
  obs: Observable<IArticleCollection[]> | undefined;

  isLoading: boolean = false;
  msg: string = '';
  searchArticle: string = '';
  noArticle: boolean = false;

  totalRows = 0;
  pageSize = 12;
  currentPage = 0;

  constructor(
    private articleService: ArticleService,
    private changeDetectorRef: ChangeDetectorRef,
    public dialog: MatDialog,
  ) {}

  ngOnInit(): void {
    this.getArticle(this.pageSize, this.currentPage, this.searchArticle);
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
  }

  getArticle(page: number, pageSize: number, searchArticle: string) {
    try {
      this.isLoading = true;
      this.articleService.getArticle(page, pageSize, searchArticle).subscribe({
        next: (res) => {
          if (!res.status || !res.data) {
            this.isLoading = false;
            this.msg = res.message;
            this.noArticle = true;
            return;
          }

          this.noArticle = false;
          this.isLoading = false;
          this.dataSource.data = res.data.result;
          this.changeDetectorRef.detectChanges();
          this.dataSource.paginator = this.paginator;
          this.obs = this.dataSource.connect();

          for (let i = 0; i < this.dataSource.data.length; i++) {
            this.dataSource.data[i].shortTitle =
              this.dataSource.data[i].title.length > 13
                ? `${this.dataSource.data[i].title.substring(0, 13)}....`
                : this.dataSource.data[i].title;
            this.dataSource.data[i].shortAuthor =
              this.dataSource.data[i].author.length > 13
                ? `${this.dataSource.data[i].author.substring(0, 13)}....`
                : this.dataSource.data[i].author;
          }

          setTimeout(() => {
            this.paginator.pageIndex = this.currentPage;
            this.paginator.length = res.data?.total ?? 0;
          });
        },
        error: (err) => {
          this.isLoading = false;
          this.msg = err.error.message;
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  pageChanged(event: PageEvent) {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.getArticle(this.currentPage, this.pageSize, this.searchArticle);
  }

  searchArticleByTitle() {
    this.getArticle(this.currentPage, this.pageSize, this.searchArticle);
    this.paginator.firstPage();
  }

  clearSearch() {
    this.searchArticle = '';
    this.getArticle(this.currentPage, this.pageSize, this.searchArticle);
  }

  openArticleDialog(action: string, data: object): void {
    let obj = { ...data, action };
    if (action == 'Delete Article') {
      const dialogRef = this.dialog.open(ArticleDialogComponent, {
        maxWidth: '1200px',
        autoFocus: false,
        data: { ...data, action },
      });

      dialogRef.afterClosed().subscribe((res) => {
        this.getArticle(this.currentPage, this.pageSize, this.searchArticle);
      });
    } else {
      const dialogRef = this.dialog.open(ArticleDialogComponent, {
        width: '70%',
        maxWidth: '1200px',
        height: '90vh',
        autoFocus: false,
        data: { ...data, action },
      });

      dialogRef.afterClosed().subscribe((res) => {
        this.getArticle(this.currentPage, this.pageSize, this.searchArticle);
      });
    }
  }
}
