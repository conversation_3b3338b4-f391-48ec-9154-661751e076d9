import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ChurchService } from 'src/app/services/church.service';
import { IAPIResponse } from 'src/app/types/interfaces/api.interface';

@Component({
  selector: 'app-church-settings',
  templateUrl: './church-settings.component.html',
  styleUrls: ['./church-settings.component.scss'],
  standalone: false,
})
export class ChurchSettingsComponent implements OnInit {
  loading: boolean = false;
  isChecked: boolean = false;
  login: boolean = false;
  isClicked: boolean = true;

  constructor(public churchService: ChurchService, private snackBar: MatSnackBar) {}

  ngOnInit(): void {
    this.getChurch();
  }

  updateChurch(login: boolean) {
    try {
      this.loading = true;
      this.churchService.updateChurch(login).subscribe({
        next: (res: IAPIResponse) => {
          this.loading = false;

          if (!res.status) {
            this.loading = false;
            this.snackBar.open(res.message, 'ok', { duration: 6000 });
            return 'error';
          }

          this.getChurch();
          this.snackBar.open(res.message, 'ok', { duration: 1000 });
          return 'success';
        },
        error: (err) => {
          this.loading = false;
          this.snackBar.open(err.error.message, 'ok', { duration: 6000 });
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  getChurch() {
    try {
      this.loading = true;
      this.churchService.getChurch().subscribe({
        next: (res) => {
          if (!res.status || !res.data) {
            this.loading = false;
            return;
          }

          this.loading = false;
          this.login = res.data.login;
        },
        error: (err) => {
          this.loading = false;
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  checkValue(e: any) {
    this.isClicked = false;
    if (e.checked === true) {
      this.isChecked = true;
    } else {
      this.isChecked = false;
    }
  }

  update() {
    if (this.isClicked) {
      this.isChecked = this.login;
    }

    this.updateChurch(this.isChecked);
  }
}
