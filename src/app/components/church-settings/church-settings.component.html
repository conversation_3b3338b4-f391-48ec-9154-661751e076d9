<!-- Spinner -->
<div *ngIf="loading" class="d-flex justify-center align-center">
  <app-spinner></app-spinner>
</div>

<!-- Title Card -->
<div class="d-flex flex-wrap">
  <div class="flex-100">
    <mat-card class="m-b-0">
      <mat-card-content>
        <mat-card-title>Church Settings</mat-card-title>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<!-- Settings Card -->
<div class="d-flex flex-wrap">
  <div class="flex-100">
    <mat-card>
      <mat-card-content>
        <div class="d-flex flex-wrap justify-between align-center gap-20">
          <div></div>

          <div class="flex-100 flex-md-25 flex-lg-25">
            <mat-checkbox [(ngModel)]="login" [ngModelOptions]="{ standalone: true }" (change)="checkValue($event)">
              Login Required
            </mat-checkbox>
          </div>

          <div class="flex-100 flex-md-30 d-flex justify-end">
            <button mat-flat-button (click)="update()" color="primary">Update</button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
