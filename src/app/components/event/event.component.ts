import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { EventService } from 'src/app/services/event.service';
import { IAPIResponse } from 'src/app/types/interfaces/api.interface';

enum Actions {
  DELETE_EVENT = 'Delete Event',
}

@Component({
  selector: 'app-event',
  templateUrl: './event.component.html',
  styleUrls: ['./event.component.scss'],
  standalone: false,
})
export class EventComponent implements OnInit {
  constructor(
    private snackBar: MatSnackBar,
    public dialogRef: MatDialogRef<EventComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private eventService: EventService,
  ) {
    this.localData = data;
    this.action = this.localData.action;
    dialogRef.disableClose = true;
  }

  loading: boolean = false;
  action: string | undefined;
  localData: any;

  ngOnInit(): void {}

  doAction() {
    const expression = this.localData.action;
    switch (expression) {
      case Actions.DELETE_EVENT:
        this.deleteEvent(this.localData._id);
        break;
    }
  }

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }

  deleteEvent(id: string) {
    try {
      this.loading = true;
      this.eventService.deleteEvent(id).subscribe({
        next: (res: IAPIResponse) => {
          if (!res.status) {
            this.loading = false;
            this.snackBar.open(res.message, 'ok', { duration: 6000 });
            return 'error';
          }

          this.loading = false;
          this.snackBar.open(res.message, 'ok', { duration: 1000 });
          this.dialogRef.close();
          return 'success';
        },
        error: (err: any) => {
          this.loading = false;
          this.snackBar.open(err.error.message, 'ok', { duration: 6000 });
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }
}
