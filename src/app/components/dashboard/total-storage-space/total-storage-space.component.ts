import { Component, OnInit, ViewChild } from '@angular/core';
import { ChartComponent } from 'ng-apexcharts';
import { ChartOptionsInterface } from 'src/app/types/interfaces/chart-options.interface';

@Component({
  selector: 'app-total-storage-space',
  templateUrl: './total-storage-space.component.html',
  styleUrls: ['./total-storage-space.component.scss'],
  standalone: false,
})
export class TotalStorageSpaceComponent implements OnInit {
  @ViewChild('chart') chart: ChartComponent = Object.create(null);
  public totalStoragePercentage!: Partial<ChartOptionsInterface> | any;

  constructor() {}

  ngOnInit(): void {
    this.totalStoragePercentageChart();
  }

  totalStoragePercentageChart() {
    this.totalStoragePercentage = {
      series: [25, 35, 40],
      chart: {
        toolbar: {
          show: false,
        },
        foreColor: '#adb0bb',
        fontFamily: "'DM Sans',sans-serif",
        type: 'donut',
        height: 280,
      },
      legend: {
        show: false,
      },
      dataLabels: {
        enabled: false,
      },
      tooltip: {
        theme: 'dark',
        fillSeriesColor: false,
      },
      labels: ['Video', 'Audio', 'Free'],
      colors: ['#03c9d7', '#fb9778', '#fec90f'],
      stroke: {
        colors: ['transparent'],
      },
      plotOptions: {
        pie: {
          donut: {
            size: '78%',
            background: 'transparent',
            labels: {
              show: false,
              name: {
                show: true,
                fontSize: '18px',
                color: undefined,
                offsetY: -10,
              },
              value: {
                show: false,
                color: '#98aab4',
              },
              total: {
                show: false,
                label: 'Our Visitors',
                color: '#98aab4',
              },
            },
          },
        },
      },
    };
  }
}
