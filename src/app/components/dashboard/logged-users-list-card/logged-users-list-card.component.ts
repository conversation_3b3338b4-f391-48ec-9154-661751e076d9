import { Component, OnInit } from '@angular/core';
import { MatRadioChange } from '@angular/material/radio';
import { DashboardService } from 'src/app/services/dashboard.service';
import { ChartOptionsInterface } from 'src/app/types/interfaces/chart-options.interface';

@Component({
  selector: 'app-logged-users-list-card',
  templateUrl: './logged-users-list-card.component.html',
  styleUrls: ['./logged-users-list-card.component.scss'],
  standalone: false,
})
export class LoggedUsersListCardComponent implements OnInit {
  public barChartOptions: Partial<ChartOptionsInterface> | any;

  xAxisData: string[] = [];
  yAxisData: number[] = [];
  chartDataModel = 'week';

  constructor(private dashboardService: DashboardService) {}

  ngOnInit(): void {
    this.getChartDetails(this.chartDataModel);
    this.loggedInUsersBarChart();
  }

  chartFilter(event: MatRadioChange) {
    this.getChartDetails(event.value);
  }

  getChartDetails(value: string) {
    try {
      this.dashboardService.getLoggedInUsersCount(value).subscribe((res) => {
        if (!res.status || !res.data) {
          return;
        }
        this.xAxisData = res.data.x;
        this.yAxisData = res.data.y;
        this.loggedInUsersBarChart();
      });
    } catch (err) {
      console.error(err);
    }
  }

  loggedInUsersBarChart() {
    this.barChartOptions = {
      series: [
        {
          name: 'users',
          data: this.yAxisData,
          color: '#fb9678',
        },
      ],
      xaxis: {
        categories: this.xAxisData,
      },
      chart: {
        type: 'bar',
        height: 370,
        fontFamily: 'DM Sans,sans-serif',
        foreColor: '#a1aab2',
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '42%',
        },
      },
      dataLabels: {
        enabled: false,
      },
      colors: ['#398bf7'],
    };
  }
}
