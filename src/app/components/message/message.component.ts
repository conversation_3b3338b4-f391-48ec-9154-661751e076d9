import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MessagesService } from 'src/app/services/messages.service';
import { IAPIResponse } from 'src/app/types/interfaces/api.interface';
enum Actions {
  DELETE_MESSAGE = 'Delete Message',
  DELETE_COMMENT = 'Delete Comment',
}

@Component({
  selector: 'app-message',
  templateUrl: './message.component.html',
  styleUrls: ['./message.component.scss'],
  standalone: false,
})
export class MessageComponent implements OnInit {
  constructor(
    private snackBar: MatSnackBar,
    public dialogRef: MatDialogRef<MessageComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private messageService: MessagesService,
  ) {
    this.localData = data;
    this.action = this.localData.action;
    dialogRef.disableClose = true;
  }

  loading: boolean = false;
  action: string;
  localData: any;

  ngOnInit(): void {}

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }

  doAction() {
    const expression = this.localData.action;

    switch (expression) {
      case Actions.DELETE_COMMENT:
        this.deleteComment(this.localData.msgId, { userId: this.localData.userId, commentId: this.localData._id });
        break;
      case Actions.DELETE_MESSAGE:
        this.deleteMessage(this.localData._id);
        break;
    }
  }

  deleteMessage(id: string) {
    try {
      this.loading = true;
      this.messageService.deleteMessage(id).subscribe({
        next: (res: IAPIResponse) => {
          if (!res.status) {
            this.loading = false;
            this.snackBar.open(res.message, 'ok', { duration: 6000 });
            return 'error';
          }

          this.loading = false;
          this.snackBar.open(res.message, 'ok', { duration: 1000 });
          this.dialogRef.close();
          return 'success';
        },
        error: (err: any) => {
          this.loading = false;
          this.snackBar.open(err.error.message, 'ok', { duration: 6000 });
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  deleteComment(id: string, data: { userId: string; commentId: string }) {
    try {
      this.loading = true;
      this.messageService.deleteComment(id, data).subscribe({
        next: (res: IAPIResponse) => {
          if (!res.status) {
            this.loading = false;
            this.snackBar.open(res.message, 'ok', { duration: 6000 });
            return 'error';
          }

          this.loading = false;
          this.snackBar.open(res.message, 'ok', { duration: 1000 });
          this.dialogRef.close();
          return 'success';
        },
        error: (err: any) => {
          this.loading = false;
          this.snackBar.open(err.error.message, 'ok', { duration: 6000 });
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }
}
