import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { NotificationService } from 'src/app/services/notification.service';
import { IAPIResponse } from 'src/app/types/interfaces/api.interface';
import { INotificationWithAction } from 'src/app/types/interfaces/notification.interface';
enum Actions {
  DELETE_NOTIFICATION = 'Delete Notification',
}

@Component({
  selector: 'app-notification-dialog',
  templateUrl: './notification-dialog.component.html',
  styleUrls: ['./notification-dialog.component.scss'],
  standalone: false,
})
export class NotificationDialogComponent implements OnInit {
  constructor(
    private notificationService: NotificationService,
    private snackBar: MatSnackBar,
    public dialogRef: MatDialogRef<NotificationDialogComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.localData = data;
    this.action = this.localData.action;
    dialogRef.disableClose = true;
  }

  loading: boolean = false;
  action: string;
  localData: INotificationWithAction; //two different obj action & data

  ngOnInit(): void {}

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }

  doAction(): void {
    const expression = this.localData.action;

    const dataPass = {
      categoryData: this.localData,
    };

    switch (expression) {
      case Actions.DELETE_NOTIFICATION:
        this.deleteNotification(this.localData._id);
        break;
    }
  }

  deleteNotification(id: string) {
    try {
      this.loading = true;
      this.notificationService.deleteNotification(id).subscribe({
        next: (res: IAPIResponse) => {
          if (!res.status) {
            this.loading = false;
            this.snackBar.open(res.message, 'ok', { duration: 6000 });
            return 'error';
          }

          this.loading = false;
          this.snackBar.open(res.message, 'ok', { duration: 1000 });
          this.dialogRef.close();
          return 'success';
        },
        error: (err) => {
          this.loading = false;
          this.snackBar.open(err.error.message, 'ok', { duration: 6000 });
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }
}
