<div *ngIf="isLoading" class="flex-center">
  <app-spinner></app-spinner>
</div>

<div class="flex-row-wrap">
  <div class="w-100">
    <mat-card>
      <mat-card-content>
        <div class="flex-space-between-center gap-20 flex-wrap">
          <div class="flex-item w-100 w-md-25">
            <mat-form-field>
              <input
                matInput
                placeholder="Search User"
                (keyup.enter)="searchUser()"
                [(ngModel)]="searchText"
                autocomplete="off" />
              <button
                *ngIf="searchText"
                mat-button
                matSuffix
                mat-icon-button
                aria-label="Clear"
                (click)="clearSearch()">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
          </div>

          <div class="flex-item w-100 w-md-10">
            <button mat-flat-button (click)="openDialog('Add Admin', {})" color="accent">
              <i-feather name="plus-circle" class="feather-15"></i-feather>
              Add Admin
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<h2 class="mat-h2 text-center m-t-10" *ngIf="noData">No admin accounts found</h2>

<div class="flex-row-wrap">
  <div class="w-100">
    <mat-card *ngIf="!noData">
      <mat-card-content>
        <mat-card-title class="m-l-10">Manage Admins</mat-card-title>
        <div class="table-responsive">
          <table mat-table [dataSource]="dataSource" class="w-100">
            <!-- table content unchanged -->
          </table>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<mat-card [ngClass]="{ invisible: noData }">
  <div class="text-end">
    <mat-paginator
      #paginator
      [length]="totalRows"
      [pageIndex]="currentPage"
      [pageSize]="pageSize"
      (page)="pageChanged($event)"
      [pageSizeOptions]="pageSizeOptions"
      aria-label="Select page">
    </mat-paginator>
  </div>
</mat-card>
