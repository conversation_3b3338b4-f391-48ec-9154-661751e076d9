import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AdminService } from 'src/app/services/admin.service';
import { IAdminCollection, IAdminWithAction } from 'src/app/types/interfaces/admin.interface';
import { IGeneratePassword } from 'src/app/types/interfaces/resetpassword.interface';
import { passwordGenerate } from 'src/app/utils/generate-password';
enum Actions {
  ACTIVATE = 'Activate Admin',
  ADD = 'Add Admin',
  DEACTIVATE = 'Deactivate Admin',
  UPDATE = 'Update Admin',
  DELETE = 'Delete Admin',
  RESEND = 'Resend Admin',
  PASSWORD = 'Generate Password',
}

@Component({
  selector: 'app-dialog-admin',
  templateUrl: './dialog-admin.component.html',
  styleUrls: ['./dialog-admin.component.scss'],
  standalone: false,
})
export class DialogAdminComponent implements OnInit {
  adminData: IAdminWithAction;
  passwordData: IGeneratePassword = {
    password: '',
    _id: '',
  };
  isLoading: boolean = false;
  isGeneratePassword: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<DialogAdminComponent>,
    private service: AdminService,
    private snackBar: MatSnackBar,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: IAdminWithAction,
  ) {
    this.adminData = data;
    dialogRef.disableClose = true;
    if (this.adminData.action === 'Generate Password') {
      this.generatePassword();
      this.isGeneratePassword = true;
      this.passwordData._id = this.adminData._id;
    }
  }

  ngOnInit(): void {}

  doAction(): void {
    const expression = this.adminData.action;
    switch (expression) {
      case Actions.ACTIVATE:
      case Actions.DEACTIVATE:
        this.activateOrDeactivateUser(this.adminData);
        break;
      case Actions.ADD:
        this.addAdmin(this.adminData);
        break;
      case Actions.UPDATE:
        this.updateUser(this.adminData);
        break;
      case Actions.RESEND:
        this.resendEmail(this.adminData);
        break;
      case Actions.DELETE:
        this.deleteUser(this.adminData);
        break;
      case Actions.PASSWORD:
        this.updateGeneratedPassword(this.passwordData);
        break;
    }
  }

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }

  activateOrDeactivateUser(data: IAdminWithAction) {
    try {
      data.isActive = !data.isActive;
      this.isLoading = true;
      this.service.activateOrDeactivateUser(data._id, data.isActive).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  addAdmin(data: IAdminCollection) {
    try {
      this.isLoading = true;
      this.service.addAdmin(data).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  updateUser(data: IAdminCollection) {
    try {
      this.isLoading = true;
      this.service.updateUser(data).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  deleteUser(data: IAdminCollection) {
    try {
      this.isLoading = true;
      this.service.deleteUser(data._id).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  resendEmail(data: IAdminCollection) {
    try {
      this.isLoading = true;
      this.service.resendEmail(data._id).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }
  async generatePassword() {
    try {
      this.passwordData.password = passwordGenerate();
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  updateGeneratedPassword(data: IGeneratePassword) {
    try {
      this.isLoading = true;
      this.service.updatePassword(data).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  onMobileInput(event: any): void {
    const input = event.target.value;
    this.adminData.mobile = input.replace(/[^0-9]/g, '');
  }
}
