/* Flex container replacing fxLayout="row wrap" */
.form-flex-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16px; /* optional spacing between rows */
}

/* Replaces fxFlex="100" / fxFlex.gt-md="100" */
.form-item {
  width: 100%;
}

/* Spacing helpers */
.m-l-15 {
  margin-left: 15px;
}

.m-r-15 {
  margin-right: 15px;
}

.pb-3 {
  padding-bottom: 1rem;
}

.pt-3 {
  padding-top: 1rem;
}

/* Error text styling */
.text-danger {
  color: #d32f2f;
}

.help-block {
  font-size: 0.875rem;
}

/* Center actions */
.text-center {
  text-align: center;
}

/* For regenerate / save / cancel buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}
