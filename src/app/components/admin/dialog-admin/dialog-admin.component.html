<h2 class="font-medium" mat-dialog-title>
  <strong>{{ adminData.action | titlecase }}</strong>
</h2>

<div *ngIf="!isGeneratePassword; else elseGeneratePasswordTemplate">
  <div class="pb-3" *ngIf="adminData.action === 'Add Admin' || adminData.action === 'Update Admin'; else elseTemplate">
    <form #userForm="ngForm">
      <mat-dialog-content>
        <div class="form-flex-container">
          <div class="form-item">
            <div class="m-r-15 m-l-15">
              <mat-form-field>
                <input
                  type="text"
                  matInput
                  required
                  id="name"
                  name="name"
                  [(ngModel)]="adminData.name"
                  placeholder="Name"
                  #title="ngModel" />
              </mat-form-field>
            </div>
          </div>

          <div class="form-item">
            <div class="m-r-15 m-l-15">
              <mat-form-field>
                <input
                  type="email"
                  matInput
                  required
                  id="email"
                  name="email"
                  placeholder="Email"
                  email
                  #userEmail="ngModel"
                  [(ngModel)]="adminData.email" />
              </mat-form-field>
              <span *ngIf="!userEmail.valid && userEmail.touched" class="help-block text-danger">
                Enter a valid Email
              </span>
            </div>
          </div>

          <div class="form-item">
            <div class="m-r-15 m-l-15">
              <mat-form-field>
                <input
                  matInput
                  type="text"
                  maxlength="10"
                  minlength="10"
                  id="mobile"
                  name="mobile"
                  [(ngModel)]="adminData.mobile"
                  placeholder="Mobile"
                  #userMobile="ngModel"
                  (input)="onMobileInput($event)" />
              </mat-form-field>
              <div *ngIf="userMobile.touched && userMobile.invalid">
                <span class="help-block text-danger">Please enter 10 digit mobile number.</span>
              </div>
            </div>
          </div>
        </div>
      </mat-dialog-content>

      <mat-dialog-actions>
        <button mat-button (click)="doAction()" mat-flat-button color="primary" [class.spinner]="isLoading">
          {{ adminData.action | titlecase }}
        </button>
        <button mat-button (click)="closeDialog()">Cancel</button>
      </mat-dialog-actions>
    </form>
  </div>

  <ng-template #elseTemplate>
    <mat-dialog-content>
      <p>
        Sure to {{ adminData.action }} <b>{{ adminData.name }}</b
        >?
      </p>
    </mat-dialog-content>
    <div mat-dialog-actions class="pt-3 text-center">
      <button mat-button [class.spinner]="isLoading" (click)="doAction()" mat-flat-button color="primary">
        {{ adminData.action }}
      </button>
      <button mat-button (click)="closeDialog()">Cancel</button>
    </div>
  </ng-template>
</div>

<ng-template #elseGeneratePasswordTemplate>
  <mat-dialog-content>
    <mat-form-field>
      <input
        type="text"
        matInput
        id="generatedPassword"
        name="generatedPassword"
        [(ngModel)]="passwordData.password"
        placeholder="Generated Password"
        #title="ngModel" />
    </mat-form-field>
  </mat-dialog-content>

  <div mat-dialog-actions class="pt-3 action-buttons">
    <button mat-button [class.spinner]="isLoading" (click)="doAction()" mat-flat-button color="primary">Save</button>
    <button mat-flat-button color="accent" (click)="generatePassword()">
      <i-feather name="refresh-cw" class="feather-14"></i-feather>
      Regenerate
    </button>
    <button mat-button (click)="closeDialog()">Cancel</button>
  </div>
</ng-template>
