/* Replaces fxLayout="row wrap" */
.flex-row-wrap {
  display: flex;
  flex-wrap: wrap;
}

/* Replaces fxLayoutAlign="space-between center" */
.flex-space-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Replaces fxLayoutGap="20px" */
.gap-20 {
  gap: 20px;
}

/* Centers spinner or loader */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Width utilities */
.w-100 {
  width: 100%;
}

/* Responsive widths (replaces fxFlex.gt-md / fxFlex.gt-lg) */
@media (min-width: 768px) {
  .w-md-25 {
    width: 25%;
  }
  .w-md-10 {
    width: 10%;
  }
}

/* Spacing and alignment utilities */
.m-t-10 {
  margin-top: 10px;
}

.text-center {
  text-align: center;
}

.text-end {
  text-align: end;
}

.invisible {
  visibility: hidden;
}

.table-responsive {
  overflow-x: auto;
}
