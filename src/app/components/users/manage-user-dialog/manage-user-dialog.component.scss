/* Flexbox replacement for fxLayout="row wrap" */
.form-flex-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16px;
}

/* Replacement for fxFlex="100" */
.form-field-wrapper {
  flex: 1 1 100%;
  margin: 0 15px;
}

/* Center alignment for dialog buttons */
.dialog-actions-center {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 1rem;
}

/* Utility */
.text-danger {
  color: #d32f2f;
}

.help-block {
  font-size: 0.85rem;
}

.spinner {
  opacity: 0.7;
  pointer-events: none;
}
