<h2 class="font-medium" mat-dialog-title>
  <strong>{{ userData.action }}</strong>
</h2>

<div *ngIf="!isGeneratePassword; else elseGeneratePasswordTemplate">
  <!-- Add / Update User -->
  <div class="pb-3" *ngIf="userData.action === 'Update' || userData.action === 'Add'; else elseTemplate">
    <form #userForm="ngForm">
      <mat-dialog-content>
        <div class="form-flex-container">
          <!-- Name -->
          <div class="form-field-wrapper">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="name"
                name="name"
                [(ngModel)]="userData.name"
                placeholder="Name"
                #title="ngModel" />
            </mat-form-field>
          </div>

          <!-- Email -->
          <div class="form-field-wrapper">
            <mat-form-field>
              <input
                type="email"
                matInput
                required
                id="email"
                name="email"
                placeholder="Email"
                email
                #userEmail="ngModel"
                [(ngModel)]="userData.email" />
            </mat-form-field>
            <span *ngIf="!userEmail.valid && userEmail.touched" class="help-block text-danger">
              Enter a valid Email
            </span>
          </div>

          <!-- Mobile -->
          <div class="form-field-wrapper">
            <mat-form-field>
              <input
                matInput
                type="text"
                maxlength="10"
                minlength="10"
                id="mobile"
                name="mobile"
                [(ngModel)]="userData.mobile"
                placeholder="Mobile"
                #userMobile="ngModel"
                (input)="onMobileInput($event)" />
            </mat-form-field>
            <div *ngIf="userMobile.touched && userMobile.invalid">
              <span class="help-block text-danger">Please enter a 10-digit mobile number.</span>
            </div>
          </div>
        </div>
      </mat-dialog-content>

      <mat-dialog-actions>
        <button mat-button (click)="doAction()" mat-flat-button color="primary" [class.spinner]="isLoading">
          {{ userData.action }}
        </button>
        <button mat-button (click)="closeDialog()">Cancel</button>
      </mat-dialog-actions>
    </form>
  </div>

  <!-- Confirmation Dialog -->
  <ng-template #elseTemplate>
    <mat-dialog-content>
      <p>
        Sure to {{ userData.action }} <b>{{ userData.name }}</b
        >?
      </p>
    </mat-dialog-content>
    <div mat-dialog-actions class="dialog-actions-center">
      <button mat-button [class.spinner]="isLoading" (click)="doAction()" mat-flat-button color="primary">
        {{ userData.action }}
      </button>
      <button mat-button (click)="closeDialog()">Cancel</button>
    </div>
  </ng-template>
</div>

<!-- Generate Password Template -->
<ng-template #elseGeneratePasswordTemplate>
  <mat-dialog-content>
    <mat-form-field>
      <input
        type="text"
        matInput
        id="generatedPassword"
        name="generatedPassword"
        [(ngModel)]="passwordData.password"
        placeholder="Generated Password"
        #title="ngModel" />
    </mat-form-field>
  </mat-dialog-content>

  <div mat-dialog-actions class="dialog-actions-center">
    <button mat-button [class.spinner]="isLoading" (click)="doAction()" mat-flat-button color="primary">Save</button>
    <button mat-flat-button color="accent" (click)="generatePassword()">
      <i-feather name="refresh-cw" class="feather-14"></i-feather>
      Regenerate
    </button>
    <button mat-button (click)="closeDialog()">Cancel</button>
  </div>
</ng-template>
