import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSelectChange } from '@angular/material/select';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { CategoryService } from 'src/app/services/category.service';
import { MediaService } from 'src/app/services/media.service';
import { ICategoryCollection } from 'src/app/types/interfaces/category.interface';
import { IMediaCollection, IPlay } from 'src/app/types/interfaces/media.interface';

import { DialogComponent } from './dialog/dialog.component';
import { MediaDialogComponent } from './media-dialog/media-dialog.component';

@Component({
  selector: 'app-media',
  templateUrl: './media.component.html',
  styleUrls: ['./media.component.scss'],
  standalone: false,
})
export class MediaComponent implements OnInit, AfterViewInit {
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator = Object.create(null);
  obs: Observable<IMediaCollection[]> | undefined;

  searchText: string = '';
  sortBy: string = 'updatedAt';
  filterBy: string = '';
  msg = '';

  action!: string;

  totalRows = 0;
  pageSize = 12;
  currentPage = 0;

  isLoading: boolean = false;
  noMedia: boolean = false;
  searchLoading: boolean = false;

  medias: IMediaCollection[] = [];
  dataSource: MatTableDataSource<IMediaCollection> = new MatTableDataSource<IMediaCollection>();
  categoryList?: ICategoryCollection[] = [];

  constructor(
    private snackBar: MatSnackBar,
    public mediaService: MediaService,
    public dialog: MatDialog,
    public datePipe: DatePipe,
    private router: Router,
    private changeDetectorRef: ChangeDetectorRef,
    private categoryService: CategoryService,
  ) {}

  ngOnInit(): void {
    this.getCategories();

    if (this.router.url == '/video') {
      this.action = 'Video';
      this.getVideos(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
      return;
    }

    if (this.router.url == '/audio') {
      this.action = 'Audio';
      this.getAudios(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
      return;
    }
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
  }

  openDialog(action: string, data: Object): void {
    let obj = { ...data, action };
    const dialogRef = this.dialog.open(DialogComponent, { data: obj });
    dialogRef.afterClosed().subscribe((result) => {
      if (this.router.url == '/video') {
        this.getVideos(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
      }

      if (this.router.url == '/audio') {
        this.getAudios(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
      }
    });
  }

  openMediaDialog(obj: IMediaCollection): void {
    if (this.router.url == '/video') {
      try {
        this.isLoading = true;

        this.mediaService.playVideo(obj.vimeoId).subscribe({
          next: (res: IPlay) => {
            if (res.status === 'available') {
              obj.playLink = res.play.progressive[0].link;
              const dialogRef = this.dialog.open(MediaDialogComponent, { data: obj });
              this.isLoading = false;
              dialogRef.afterClosed();
              return;
            }

            this.isLoading = false;
            this.snackBar.open('Video is uploading , wait for some time', 'ok', { duration: 5000 });
          },
          error: (err) => {
            this.isLoading = false;
          },
        });
      } catch (error) {
        console.error(error);
        this.isLoading = false;
      }
      return;
    }

    const dialogRef = this.dialog.open(MediaDialogComponent, { data: obj });
    dialogRef.afterClosed().subscribe((result) => {});
  }

  getVideos(page: number, pageSize: number, sortBy: string, filterBy: string, search: string) {
    try {
      this.isLoading = true;
      this.mediaService.getAllVideos(page, pageSize, sortBy, filterBy, search).subscribe({
        next: (res) => {
          if (!res.status || !res.data) {
            this.isLoading = false;
            this.noMedia = true;
            this.msg = res.message;
            return;
          }

          this.noMedia = false;
          this.isLoading = false;
          this.dataSource.data = res.data.result;
          this.changeDetectorRef.detectChanges();
          this.dataSource.paginator = this.paginator;
          this.obs = this.dataSource.connect();
          for (let i = 0; i < this.dataSource.data.length; i++) {
            this.dataSource.data[i].shortTitle =
              this.dataSource.data[i].title.length > 13
                ? `${this.dataSource.data[i].title.substring(0, 13)}....`
                : this.dataSource.data[i].title;
            this.dataSource.data[i].shortAuthor =
              this.dataSource.data[i].author.length > 13
                ? `${this.dataSource.data[i].author.substring(0, 13)}....`
                : this.dataSource.data[i].author;
            this.dataSource.data[i].mediaDuration = this.secondsToHms(this.dataSource.data[i].duration);
          }

          setTimeout(() => {
            this.paginator.pageIndex = this.currentPage;
            this.paginator.length = res.data?.total ?? 0;
          });
        },
        error: (err) => {
          this.isLoading = false;
          this.msg = err.error.message;
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  getAudios(page: number, pageSize: number, sortBy: string, filterBy: string, search: string) {
    try {
      this.isLoading = true;
      this.mediaService.getAllAudios(page, pageSize, sortBy, filterBy, search).subscribe({
        next: (res) => {
          if (!res.status || !res.data) {
            this.isLoading = false;
            this.noMedia = true;
            this.msg = res.message;
            return;
          }

          this.noMedia = false;
          this.isLoading = false;
          this.dataSource.data = res.data.result;
          this.changeDetectorRef.detectChanges();
          this.dataSource.paginator = this.paginator;
          this.obs = this.dataSource.connect();
          for (let i = 0; i < this.dataSource.data.length; i++) {
            this.dataSource.data[i].shortTitle =
              this.dataSource.data[i].title.length > 13
                ? `${this.dataSource.data[i].title.substring(0, 13)}....`
                : this.dataSource.data[i].title;
            this.dataSource.data[i].shortAuthor =
              this.dataSource.data[i].author.length > 13
                ? `${this.dataSource.data[i].author.substring(0, 13)}....`
                : this.dataSource.data[i].author;
            this.dataSource.data[i].mediaDuration = this.secondsToHms(this.dataSource.data[i].duration);
          }

          setTimeout(() => {
            this.paginator.pageIndex = this.currentPage;
            this.paginator.length = res.data?.total ?? 0;
          });
        },
        error: (err) => {
          this.isLoading = false;
          this.msg = err.error.message;
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  secondsToHms(second: string) {
    let duration_seconds = Number(second);
    let hours = Math.floor(duration_seconds / 3600);
    let minutes = Math.floor((duration_seconds - hours * 3600) / 60);
    let seconds = duration_seconds - hours * 3600 - minutes * 60;
    return (
      hours.toString().padStart(2, '0') +
      ':' +
      minutes.toString().padStart(2, '0') +
      ':' +
      seconds.toString().padStart(2, '0')
    );
  }

  filterByChange(event: MatSelectChange) {
    if (this.router.url == '/video') {
      this.getVideos(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
    }

    if (this.router.url == '/audio') {
      this.getAudios(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
    }

    this.paginator.firstPage();
  }

  sortByChange(event: MatSelectChange) {
    if (this.router.url == '/video') {
      this.getVideos(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
    }

    if (this.router.url == '/audio') {
      this.getAudios(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
    }
  }

  getCategories() {
    try {
      if (this.router.url == '/video') {
        this.categoryService.getAllCategoriesByVideo().subscribe((res) => {
          if (!res.status || !res.data) {
            return;
          }

          this.categoryList = res.data;
        });
      }

      if (this.router.url == '/audio') {
        this.categoryService.getAllCategoriesByAudio().subscribe((res) => {
          if (!res.status || !res.data) {
            return;
          }

          this.categoryList = res.data;
        });
      }
    } catch (err) {
      console.error(err);
    }
  }

  pageChanged(event: PageEvent) {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    if (this.router.url == '/video') {
      this.getVideos(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
    }

    if (this.router.url == '/audio') {
      this.getAudios(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
    }
  }

  searchMedia() {
    this.searchLoading = true;
    if (this.router.url == '/video') {
      this.getVideos(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
    }

    if (this.router.url == '/audio') {
      this.getAudios(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
    }

    this.paginator.firstPage();
    this.searchLoading = false;
  }

  clearSearch() {
    this.searchText = '';
    if (this.router.url == '/video') {
      this.getVideos(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
    }

    if (this.router.url == '/audio') {
      this.getAudios(this.currentPage, this.pageSize, this.sortBy, this.filterBy, this.searchText);
    }
  }
}
