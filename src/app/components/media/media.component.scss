.media-thumbnail {
  position: relative;
  display: inline-block;
  cursor: pointer;
  text-align: center;

  &:before {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    content: '\e1c4';
    font-family: 'Material Icons';
    font-size: 80px;
    color: #fff;
    opacity: 0.8;
    text-shadow: 0px 0px 30px rgba(0, 0, 0, 0.5);
  }
  &:hover:before {
    color: #eee;
  }
}
/* === Flex Utilities === */
.d-flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.text-right {
  text-align: right;
}

.gap-20 {
  gap: 20px;
}

/* === Flex Width Helpers === */
.flex-100 {
  flex: 1 1 100%;
}

.flex-40 {
  flex: 1 1 40%;
}

.flex-60 {
  flex: 1 1 60%;
}

/* Responsive replacements for fxFlex breakpoints */
@media (min-width: 600px) {
  .flex-sm-33 {
    flex: 1 1 33.33%;
  }
}

@media (min-width: 960px) {
  .flex-md-10 {
    flex: 1 1 10%;
  }

  .flex-md-30 {
    flex: 1 1 30%;
  }
}

/* === Additional Styles === */
.media-content {
  margin-top: 1rem;
}

.media-card-footer {
  padding-top: 10px;
}

.mat-card-content-custom {
  padding: 10px 0;
}

.media-thumbnail img {
  width: 100%;
  height: auto;
  border-radius: 4px;
}

.list-card-custom {
  margin: 10px;
  border-radius: 8px;
}

.mat-ripple-style {
  height: 200px;
}

.media-card-footer {
  margin: 0px 20px 0px 0px !important;
}

.media-content {
  min-height: 60vh;
}
