<!-- Spinner -->
<div *ngIf="isLoading" class="d-flex justify-center align-center">
  <app-spinner></app-spinner>
</div>

<div class="media-body">
  <!-- Search and Filters -->
  <div class="d-flex flex-wrap">
    <div class="flex-100">
      <mat-card>
        <mat-card-content>
          <div class="d-flex flex-wrap justify-between align-center gap-20">
            <div class="flex-100 flex-md-30">
              <mat-form-field>
                <input
                  matInput
                  placeholder="Search {{ action }}"
                  name="search"
                  (keyup.enter)="searchMedia()"
                  [(ngModel)]="searchText"
                  autocomplete="off" />
                <button
                  *ngIf="searchText"
                  mat-button
                  matSuffix
                  mat-icon-button
                  aria-label="Clear"
                  (click)="clearSearch()">
                  <mat-icon>close</mat-icon>
                </button>
              </mat-form-field>
            </div>

            <div class="flex-100 flex-md-10">
              <mat-form-field>
                <mat-label>Sort By</mat-label>
                <mat-select [(ngModel)]="sortBy" name="sortBy" (selectionChange)="sortByChange($event)">
                  <mat-option value="createdAt">Created Date</mat-option>
                  <mat-option value="updatedAt">Updated Date</mat-option>
                  <mat-option value="ascending">Ascending By Title</mat-option>
                  <mat-option value="descending">Descending By Title</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="flex-100 flex-md-10">
              <mat-form-field>
                <mat-label>Filter By</mat-label>
                <mat-select name="filterBy" [(ngModel)]="filterBy" (selectionChange)="filterByChange($event)">
                  <mat-option value="">All</mat-option>
                  <mat-option *ngFor="let c of categoryList" [value]="c._id">{{ c.name }}</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="flex-100 flex-md-10">
              <button
                *ngIf="action === 'Video'; else audio"
                mat-flat-button
                (click)="openDialog('Add Video', {})"
                color="accent">
                <i-feather name="plus-circle" class="feather-15"></i-feather>
                Add {{ action }}
              </button>
              <ng-template #audio>
                <button mat-flat-button (click)="openDialog('Add Audio', {})" color="accent">
                  <i-feather name="plus-circle" class="feather-15"></i-feather>
                  Add {{ action }}
                </button>
              </ng-template>
            </div>

            <div class="flex-100 flex-md-10">
              <button
                mat-flat-button
                color="primary"
                (click)="searchMedia()"
                [class.spinner]="searchLoading"
                [disabled]="searchLoading">
                <i-feather name="refresh-cw" class="feather-15"></i-feather>
                Reload
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <h2 class="mat-h2 text-center" *ngIf="noMedia">{{ msg }} !</h2>

  <!-- Media list -->
  <div class="d-flex flex-wrap media-content">
    <div class="flex-100 flex-sm-33" *ngFor="let media of obs | async">
      <mat-card *ngIf="!noMedia" class="list-card-custom d-flex">
        <div class="flex-40">
          <div class="mat-ripple-style" matRipple (click)="openMediaDialog(media)">
            <div class="media-thumbnail">
              <img class="list-card-left-image-custom" [src]="media.thumbnail" alt="Thumbnail of media" />
            </div>
          </div>
        </div>

        <div class="flex-60">
          <mat-card-content class="mat-card-content-custom">
            <mat-card-title class="mat-subheading-1" [title]="media.title">
              <span class="mat-card-title-custom-cls"> {{ media.shortTitle | titlecase }}</span>
            </mat-card-title>

            <mat-card-subtitle
              class="m-b-0 m-t-10 text-muted fw-medium d-flex justify-between align-center"
              [title]="media.author">
              {{ media.shortAuthor | titlecase }}
            </mat-card-subtitle>

            <mat-card-subtitle class="m-t-10">
              {{ media.mediaDuration }}
            </mat-card-subtitle>

            <mat-card-subtitle class="text-muted fw-medium">
              Status:
              <span *ngIf="media.status === 'available'; else uploadingStatus" class="text-success feather-18">
                {{ media.status | titlecase }}
              </span>
              <ng-template #uploadingStatus>
                <span class="text-danger feather-18"> {{ media.status | titlecase }}</span>
              </ng-template>
            </mat-card-subtitle>
          </mat-card-content>

          <mat-card-footer class="media-card-footer text-right">
            <div *ngIf="action === 'Video'; else audios">
              <a (click)="openDialog('Update Video', media)" class="m-r-10 cursor-pointer" title="Edit">
                <i-feather name="edit" class="text-primary feather-18"></i-feather>
              </a>
              <a (click)="openDialog('Delete Video', media)" class="ml-auto cursor-pointer" title="Delete">
                <i-feather name="trash" class="text-danger feather-18"></i-feather>
              </a>
            </div>
            <ng-template #audios>
              <a (click)="openDialog('Update Audio', media)" class="m-r-10 cursor-pointer" title="Edit">
                <i-feather name="edit" class="text-primary feather-18"></i-feather>
              </a>
              <a (click)="openDialog('Delete Audio', media)" class="ml-auto cursor-pointer" title="Delete">
                <i-feather name="trash" class="text-danger feather-18"></i-feather>
              </a>
            </ng-template>
          </mat-card-footer>
        </div>
      </mat-card>
    </div>
  </div>

  <mat-card>
    <div class="text-right">
      <mat-paginator
        #paginator
        [length]="totalRows"
        [pageIndex]="currentPage"
        [pageSize]="pageSize"
        (page)="pageChanged($event)"
        [pageSizeOptions]="[12, 24, 36, 48]"
        aria-label="Select page">
      </mat-paginator>
    </div>
  </mat-card>
</div>
