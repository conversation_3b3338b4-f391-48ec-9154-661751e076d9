import { DatePipe } from '@angular/common';
import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { lastValueFrom } from 'rxjs';
import { CategoryService } from 'src/app/services/category.service';
import { MediaService } from 'src/app/services/media.service';
import { SourceTypes } from 'src/app/types/enums/media.enum';
import { AWSResponse, IAPIResponse } from 'src/app/types/interfaces/api.interface';
import { ICategoryCollection } from 'src/app/types/interfaces/category.interface';
import { IMediaCollection, IMediaUpload } from 'src/app/types/interfaces/media.interface';
import { v4 as uuid } from 'uuid';

import { environment } from '../../../../environments/environment';

interface LocalData extends IMediaCollection {
  action: string;
  name: string;
}

enum Actions {
  ADD_VIDEO = 'Add Video',
  UPDATE_VIDEO = 'Update Video',
  DELETE_VIDEO = 'Delete Video',
  ADD_AUDIO = 'Add Audio',
  UPDATE_AUDIO = 'Update Audio',
  DELETE_AUDIO = 'Delete Audio',
}

@Component({
  selector: 'app-dialog',
  templateUrl: './dialog.component.html',
  styleUrls: ['./dialog.component.scss'],
  standalone: false,
})
export class DialogComponent implements OnInit {
  localData!: LocalData; //two different obj action & data
  imageUpload!: File;
  mediaUpload!: File;
  isAdd: boolean = false;
  action: string;
  categoryList?: ICategoryCollection[] = [];
  loading: boolean = false;
  mediaFileName?: string;

  constructor(
    private categoryService: CategoryService,
    private mediaService: MediaService,
    public datePipe: DatePipe,
    public dialogRef: MatDialogRef<DialogComponent>,
    private snackBar: MatSnackBar,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: LocalData,
  ) {
    this.localData = { ...data };
    this.action = this.localData.action;
    dialogRef.disableClose = true;
    if (!this.localData.thumbnail) {
      this.localData.thumbnail = 'assets/images/logos/logo.png';
    }

    if (this.action === Actions.ADD_VIDEO || this.action === Actions.ADD_AUDIO) {
      this.isAdd = true;
    }
  }

  ngOnInit(): void {
    this.getCategories();
  }

  getCategories() {
    try {
      if (this.action === Actions.ADD_VIDEO || this.action === Actions.UPDATE_VIDEO) {
        this.categoryService.getAllCategoriesByVideo().subscribe((res) => {
          if (!res.status || !res.data) {
            return;
          }

          let response = <IAPIResponse<ICategoryCollection[]>>res;
          this.categoryList = response.data;
        });
      }

      if (this.action === Actions.ADD_AUDIO || this.action === Actions.UPDATE_AUDIO) {
        this.categoryService.getAllCategoriesByAudio().subscribe((res) => {
          if (!res.status || !res.data) {
            return;
          }

          let response = <IAPIResponse<ICategoryCollection[]>>res;
          this.categoryList = response.data;
        });
      }
    } catch (err) {
      console.error(err);
    }
  }

  doAction(): void {
    if (!this.localData) {
      return;
    }

    const expression = this.localData.action;
    if (expression === Actions.ADD_AUDIO || expression === Actions.ADD_VIDEO) {
      if (!this.imageUpload || !this.mediaUpload) {
        this.snackBar.open('Select media and thumbnail', 'ok', { duration: 5000 });
        return;
      }
    }

    const dataPass = {
      mediaData: this.localData,
      uploadImage: this.imageUpload,
      uploadMedia: this.mediaUpload,
    };

    switch (expression) {
      case Actions.ADD_VIDEO:
        this.addVideo(dataPass);
        break;
      case Actions.ADD_AUDIO:
        this.addAudio(dataPass);
        break;
      case Actions.UPDATE_VIDEO:
        this.updateMedia(dataPass);
        break;
      case Actions.DELETE_VIDEO:
        this.deleteMedia(this.localData);
        break;
      case Actions.UPDATE_AUDIO:
        this.updateMedia(dataPass);
        break;
      case Actions.DELETE_AUDIO:
        this.deleteMedia(this.localData);
        break;
    }
  }

  addVideo(rowObj: IMediaUpload): void {
    try {
      this.loading = true;
      this.mediaService.uploadVideo(rowObj.mediaData, rowObj.uploadImage, rowObj.uploadMedia).then((promise) => {
        if (!promise) {
          this.loading = false;
          this.snackBar.open('Failed to upload ', 'ok', { duration: 6000 });
          return;
        }

        promise.subscribe({
          next: (res) => {
            this.loading = false;

            const response = <IAPIResponse>res;
            if (response.status === false) {
              this.loading = false;
              this.snackBar.open(response.message, 'ok', { duration: 6000 });
              return 'error';
            }

            this.loading = false;
            this.snackBar.open(
              'Video Upload will take 5 to 10 minutes to check the status click on reload button',
              'ok',
              { duration: 6000 },
            );
            this.dialogRef.close();
            return 'success';
          },
          error: (err) => {
            this.loading = false;
          },
        });
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  async addAudio(rowObj: IMediaUpload): Promise<void | boolean | string> {
    try {
      let isUploadedToS3 = false;
      this.loading = true;
      const sourceFile = uuid();
      const name = `${sourceFile}.${rowObj.uploadMedia.name.split('.').pop()}`;
      const res = await lastValueFrom(this.mediaService.getSignedUrl(name, rowObj.uploadMedia.type, SourceTypes.AUDIO));

      if (!res.data && environment.production) {
        this.snackBar.open('No data found', 'ok', { duration: 6000 });
        this.loading = false;
        return 'error';
      }

      if (res.data) {
        const formData = new FormData();
        formData.append('file', rowObj.uploadMedia);
        const awsRes = await lastValueFrom<AWSResponse>(
          this.mediaService.uploadWithSignedUrl(res.data, rowObj.uploadMedia.type, formData),
        );

        this.loading = false;
        isUploadedToS3 = true;
        if (awsRes.status !== 200) {
          this.snackBar.open('Cannot upload', 'ok', { duration: 6000 });
          return 'error';
        }
      }

      this.mediaService
        .uploadAudio(rowObj.mediaData, rowObj.uploadImage, name, isUploadedToS3 ? undefined : rowObj.uploadMedia)
        .subscribe({
          next: (res) => {
            this.loading = false;
            const response = <IAPIResponse>res;
            if (response.status === false) {
              this.snackBar.open(response.message, 'ok', { duration: 6000 });
              return 'error';
            }

            this.snackBar.open(response.message, 'ok', { duration: 3000 });
            this.dialogRef.close();
            return 'success';
          },
          error: (err) => {
            this.loading = false;
          },
        });
    } catch (err) {
      console.error(err);
      this.loading = false;
      return false;
    }
  }

  selectImage(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    let fileList: FileList | null = element.files;
    if (!fileList || fileList.length === 0) {
      return;
    }

    const mimeType = fileList[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(fileList[0]);
    reader.onload = (_event) => {
      this.localData!.thumbnail = reader.result as unknown as string;
    };
    this.imageUpload = <File>fileList[0];
  }

  selectMedia(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    let fileList: FileList | null = element.files;
    if (!fileList || fileList.length === 0) {
      return;
    }

    const mediaDuration = new Audio();
    mediaDuration.src = URL.createObjectURL(fileList[0]);
    mediaDuration.onloadedmetadata = () => {
      this.localData.duration = (mediaDuration.duration || 0).toFixed(0);
    };

    this.mediaUpload = <File>fileList[0];
    this.mediaFileName =
      this.mediaUpload.name.length > 30
        ? `${this.mediaUpload.name.substring(0, 20)}.... .${this.mediaUpload.name.split('.').pop()}`
        : this.mediaUpload.name;
    const mimeType = fileList[0].type;
    if (mimeType.match(/video\/*/) == null) {
      return;
    }

    if (mimeType.match(/audio\/*/) == null) {
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(fileList[0]);
    reader.onload = (_event) => {
      this.localData!.media = reader.result as unknown as string;
    };
  }

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }

  updateMedia(data: IMediaUpload) {
    try {
      this.loading = true;
      this.mediaService.updateMedia(data.mediaData).subscribe({
        next: (res) => {
          let response = <IAPIResponse>res;
          if (response.status === false) {
            this.loading = false;
            this.snackBar.open(response.message, 'ok', {
              duration: 6000,
            });
            return 'error';
          }

          this.loading = false;
          this.snackBar.open(response.message, 'ok', { duration: 1000 });
          this.dialogRef.close();
          return 'success';
        },
        error: (err) => {
          this.loading = false;
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  deleteMedia(data: IMediaCollection) {
    try {
      this.loading = true;
      this.mediaService.deleteMedia(data._id).subscribe({
        next: (res) => {
          let response = <IAPIResponse>res;
          if (response.status === false) {
            this.loading = false;
            this.snackBar.open(response.message, 'ok', { duration: 6000 });
            return 'error';
          }
          this.loading = false;
          this.snackBar.open(response.message, 'ok', { duration: 1000 });
          this.dialogRef.close();
          return 'success';
        },
        error: (err) => {
          this.loading = false;
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  clearUploads(type: string) {
    if (type === 'thumbnail') {
      this.localData.thumbnail = '';
      this.localData.thumbnail = 'assets/images/logos/logo.png';
      this.imageUpload = undefined as unknown as File;
      return;
    }
    this.localData.media = '';
    this.mediaFileName = '';
    this.mediaUpload = undefined as unknown as File;
  }
}
