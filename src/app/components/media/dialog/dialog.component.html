<h2 class="font-medium" mat-dialog-title>
  <strong>{{ action }}</strong>
</h2>

<div
  class="pb-3"
  *ngIf="
    action === 'Add Video' || action === 'Add Audio' || action === 'Update Video' || action === 'Update Audio';
    else elseTemplate
  ">
  <form #mediaForm="ngForm">
    <mat-dialog-content>
      <!-- File Upload Row -->
      <div *ngIf="isAdd" class="flex-row flex-wrap align-center">
        <!-- Media upload -->
        <div class="flex-50">
          <div class="m-r-15 m-l-15">
            <div class="flex-row align-center m-b-15">
              <div class="flex-col">
                <img class="upload-image" src="assets/images/logos/file-upload.png" />
                <button
                  *ngIf="mediaUpload"
                  mat-raised-button
                  aria-label="Clear"
                  class="clear-image"
                  (click)="clearUploads('media')">
                  <mat-icon>highlight_off</mat-icon>Clear
                </button>
              </div>

              <div class="flex-col">
                <button mat-raised-button color="accent" class="m-l-15 input-file-button">
                  <div *ngIf="action === 'Add Video'">
                    <label for="media">Select video</label>
                    <input type="file" accept="video/*" id="media" required (change)="selectMedia($event)" #fileInput />
                  </div>

                  <div *ngIf="action === 'Add Audio'">
                    <label for="media">Select audio</label>
                    <input
                      type="file"
                      accept="audio/mpeg, audio/mpg, audio/mp3"
                      id="media"
                      required
                      (change)="selectMedia($event)" />
                  </div>
                </button>

                <label *ngIf="action === 'Add Video'" class="image-informative-cls m-t-15">
                  Acceptable type: mp4
                </label>
                <label *ngIf="action === 'Add Audio'" class="image-informative-cls m-t-15">
                  Acceptable type: mpeg, mpg, mp3
                </label>
              </div>

              <label class="m-l-5">{{ mediaFileName && mediaFileName }}</label>
            </div>
          </div>
        </div>

        <!-- Thumbnail upload -->
        <div class="flex-50">
          <div class="m-r-15 m-l-15">
            <div class="flex-row align-center m-b-15">
              <div class="flex-col">
                <img class="upload-image" [src]="localData.thumbnail" onerror="assets/images/logos/logo.png" />
                <button *ngIf="imageUpload" mat-raised-button class="clear-image" (click)="clearUploads('thumbnail')">
                  <mat-icon>highlight_off</mat-icon>Clear
                </button>
              </div>

              <div class="flex-col m-l-15">
                <button mat-raised-button color="primary" class="input-file-button">
                  <label for="images">Select thumbnail</label>
                  <input
                    type="file"
                    id="images"
                    accept="image/png, image/jpeg, image/jpg"
                    required
                    (change)="selectImage($event)" />
                </button>

                <label class="image-informative-cls m-t-15">Acceptable type: png, jpg, jpeg</label>
                <label class="image-informative-cls">Upload limit: 15MB</label>
                <label class="image-informative-cls">Width: 150px Height: 150px</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Text Fields -->
      <div class="flex-row flex-wrap align-center">
        <div class="flex-100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="title"
                name="title"
                [(ngModel)]="localData.title"
                placeholder="Title"
                #title="ngModel"
                #input
                maxlength="50" />
              <mat-hint align="end">{{ input.value.length || 0 }}/50</mat-hint>
            </mat-form-field>
          </div>
        </div>

        <div class="flex-100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="author"
                name="author"
                placeholder="Author"
                #author="ngModel"
                [(ngModel)]="localData.author"
                #inputAuthor
                maxlength="26" />
              <mat-hint align="end">{{ inputAuthor.value.length || 0 }}/26</mat-hint>
            </mat-form-field>
          </div>
        </div>

        <div class="flex-100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="description"
                name="description"
                placeholder="Description"
                [(ngModel)]="localData.description" />
            </mat-form-field>
          </div>
        </div>

        <div class="flex-100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <mat-select name="channelId" [(ngModel)]="localData.channelId" placeholder="Category" required>
                <mat-option *ngFor="let c of categoryList" [value]="c._id">{{ c.name }}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

        <div *ngIf="isAdd" class="flex-100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                matInput
                type="number"
                maxlength="10"
                id="duration"
                name="duration"
                [(ngModel)]="localData.duration"
                placeholder="Duration (sec)" />
            </mat-form-field>
          </div>
        </div>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions>
      <button
        mat-button
        (click)="doAction()"
        mat-flat-button
        color="primary"
        [class.spinner]="loading"
        [disabled]="!mediaForm.valid || loading">
        {{ action }}
      </button>
      <button mat-button (click)="closeDialog()">Cancel</button>
    </mat-dialog-actions>
  </form>
</div>

<ng-template #elseTemplate>
  <p>
    Sure to {{ action }} <b>{{ localData.title }}</b
    >?
  </p>
  <div mat-dialog-actions align="center" class="pt-3">
    <button
      mat-button
      (click)="doAction()"
      mat-flat-button
      color="primary"
      [class.spinner]="loading"
      [disabled]="loading">
      {{ action }}
    </button>
    <button mat-button (click)="closeDialog()">Cancel</button>
  </div>
</ng-template>
