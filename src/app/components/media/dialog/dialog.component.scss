.clear-image {
  padding: 0px !important;
  color: red;
}
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-50 {
  flex: 1 1 50%;
  max-width: 50%;
}

.flex-100 {
  flex: 1 1 100%;
  max-width: 100%;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.space-between {
  justify-content: space-between;
}

.m-r-15 {
  margin-right: 15px;
}

.m-l-15 {
  margin-left: 15px;
}

.m-t-15 {
  margin-top: 15px;
}

.m-b-15 {
  margin-bottom: 15px;
}

.m-l-5 {
  margin-left: 5px;
}

.pb-3 {
  padding-bottom: 1rem;
}

.pt-3 {
  padding-top: 1rem;
}
