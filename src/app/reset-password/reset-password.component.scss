.mat-icon {
  font-size: 90px;
  width: 100%;
  height: 50px;
}
button {
  width: 100%;
  height: 100%;
}
.password-icon {
  font-size: 20px;
}
/* Generic Flex Utilities */
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* Wrapper Layout */
.auth-wrapper {
  display: flex;
  flex-wrap: wrap;
  min-height: 100vh;
}

/* Left Section */
.auth-left {
  flex: 1 1 100%;
  display: flex;
  background-color: #fff;
}

@media (min-width: 960px) {
  .auth-left {
    flex: 0 0 60%;
  }
}

/* Right Section */
.auth-right {
  flex: 1 1 100%;
  display: flex;
  align-items: center;
}

@media (min-width: 960px) {
  .auth-right {
    flex: 0 0 40%;
  }
}

/* Inner content */
.detail-part {
  width: 100%;
  min-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.right-bg-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}

.form-container {
  flex: 1 1 100%;
  max-width: 70%;
}

@media (min-width: 1280px) {
  .form-container {
    max-width: 50%;
  }
}

/* Other UI */
.text-center {
  text-align: center;
}

.text-success {
  color: #4caf50;
}

.text-primary {
  color: #3f51b5;
}

.fw-bold {
  font-weight: 600;
}

.m-t-10 {
  margin-top: 10px;
}

.m-t-30 {
  margin-top: 30px;
}

.m-b-10 {
  margin-bottom: 10px;
}

.w-100 {
  width: 100%;
}

.p-30 {
  padding: 30px;
}

.error-card {
  padding: 20px;
}

.password-icon {
  cursor: pointer;
}
