<div *ngIf="isLoading" class="flex-row flex-center">
  <app-spinner></app-spinner>
</div>

<div class="auth-wrapper flex-row flex-wrap">
  <!-- Left Section -->
  <div class="auth-left bg-white bg-church-landing-page">
    <div class="detail-part flex-row flex-center">
      <div class="text-center bg-church-landing-page-logo">
        <img src="{{ info.logo }}" appLogoOnError />
        <h2 class="m-t-10 fw-bold">{{ info.content | uppercase }}</h2>
      </div>
    </div>
  </div>

  <!-- Right Section -->
  <div class="auth-right flex-row flex-center">
    <div class="right-bg-content flex-row flex-wrap">
      <div class="form-container">
        <div class="p-30">
          <!-- Form Section -->
          <div *ngIf="isToken; else elseToken">
            <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()" class="m-t-30">
              <h2 class="fw-bold m-b-10 text-primary text-center">Reset Password</h2>

              <!-- Password -->
              <mat-form-field appearance="outline">
                <mat-label>Password</mat-label>
                <input
                  matInput
                  formControlName="password"
                  name="password"
                  placeholder="Password"
                  [type]="passwordHide ? 'password' : 'text'"
                  [ngClass]="{ 'is-invalid': submitted && f['password'].errors }" />
                <mat-icon class="text-primary password-icon" matSuffix (click)="passwordHide = !passwordHide">
                  {{ passwordHide ? 'visibility' : 'visibility_off' }}
                </mat-icon>
              </mat-form-field>

              <div *ngIf="submitted && f['password'].errors" class="invalid-feedback">
                <div *ngIf="f['password'].errors['required']">Password is required</div>
                <div *ngIf="f['password'].errors['minlength']">Password must be at least 6 characters</div>
              </div>

              <!-- Confirm Password -->
              <mat-form-field appearance="outline">
                <mat-label>Confirm Password</mat-label>
                <input
                  matInput
                  formControlName="confirmPassword"
                  name="confirmPassword"
                  placeholder="Confirm Password"
                  [type]="confirmPasswordHide ? 'password' : 'text'"
                  [ngClass]="{ 'is-invalid': submitted && f['confirmPassword'].errors }" />
                <mat-icon
                  class="text-primary password-icon"
                  matSuffix
                  (click)="confirmPasswordHide = !confirmPasswordHide">
                  {{ confirmPasswordHide ? 'visibility' : 'visibility_off' }}
                </mat-icon>
              </mat-form-field>

              <div *ngIf="submitted && f['confirmPassword'].errors" class="invalid-feedback">
                <div *ngIf="f['confirmPassword'].errors['required']">Confirm Password is required</div>
                <div *ngIf="f['confirmPassword'].errors['matching']">Password and confirm password not match</div>
              </div>

              <button mat-flat-button color="primary" class="w-100" type="submit">Reset</button>
            </form>
          </div>

          <!-- Success/Failure Message -->
          <ng-template #elseToken>
            <div class="m-t-30">
              <div class="error-card text-center">
                <div *ngIf="isSuccess; else elseCondition">
                  <mat-icon class="text-success">done</mat-icon>
                </div>
                <ng-template #elseCondition>
                  <mat-icon color="warn" class="mat-icon notranslate material-icons mat-icon-no-color">
                    highlight_off
                  </mat-icon>
                </ng-template>
                <h2 class="mat-h2">{{ msg }} !</h2>
              </div>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
  </div>
</div>
