import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IAdminCollection } from '../types/interfaces/admin.interface';
import { IAdminNotification } from '../types/interfaces/admin-notifications';
import { IAPIResponse, IPaginationResponse } from '../types/interfaces/api.interface';
import { IChurchCollection } from '../types/interfaces/church.interface';
import { IGeneratePassword } from '../types/interfaces/resetpassword.interface';
import { IUserCollection } from '../types/interfaces/user.interface';
import { AuthUtils } from '../utils/auth';

@Injectable({
  providedIn: 'root',
})
export class AdminService {
  constructor(private httpClient: HttpClient) {}

  getChurchByToken(token: string) {
    return this.httpClient.get<IAPIResponse<IUserCollection[]>>(`${baseUrl}/church-by-token/` + token);
  }

  updateChurchAdmin(token: string, formData: IChurchCollection) {
    const churchAdminData = {
      name: formData.name,
      email: formData.email,
      mobile: formData.mobile,
      password: formData.password,
    };

    return this.httpClient.put<IAPIResponse<IUserCollection[]>>(`${baseUrl}/signup/` + token, churchAdminData);
  }

  getAllUsers(currentPage: number, pageSize: number, search: string) {
    return this.httpClient.get<IAPIResponse<IPaginationResponse<IUserCollection[]>>>(
      `${baseUrl}/users?currentPage=${currentPage}&pageSize=${pageSize}&search=${search}`,
    );
  }
  getAllAdmins(currentPage: number, pageSize: number, search: string) {
    return this.httpClient.get<IAPIResponse<IPaginationResponse<IUserCollection[]>>>(
      `${baseUrl}/list?currentPage=${currentPage}&pageSize=${pageSize}&search=${search}`,
    );
  }

  activateOrDeactivateUser(id: string, isActive: boolean) {
    const isActives = { isActive: isActive };
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/deactivate-user/${id}`, isActives);
  }

  updateUser(data: IUserCollection | IAdminCollection) {
    const dataPass = {
      name: data.name,
      email: data.email,
      mobile: data.mobile,
    };

    return this.httpClient.put<IAPIResponse>(`${baseUrl}/user/${data._id}`, dataPass);
  }

  addAdmin(data: IAdminCollection) {
    const dataPass = {
      name: data.name,
      email: data.email,
      mobile: data.mobile,
      password: '',
    };
    return this.httpClient.post<IAPIResponse>(`${baseUrl}/register`, dataPass);
  }

  deleteUser(_id: string) {
    return this.httpClient.delete<IAPIResponse>(`${baseUrl}/delete-user/${_id}`);
  }

  forgotPassword(email: string) {
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/forgot-password/${email}`, null);
  }

  getAdminId() {
    const adminAuthData = AuthUtils.getAuthStorage();
    return adminAuthData?.userId;
  }

  getAdminNotifications() {
    return this.httpClient.get<IAPIResponse<IAdminNotification[]>>(`${baseUrl}/admin-notifications`);
  }

  readAdminNotification(id: string, data: boolean) {
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/read-admin-notification/${id}`, { markAsRead: data });
  }

  clearAdminNotification(data: boolean) {
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/clear-admin-notification`, { markAsRead: data });
  }

  resendEmail(_id: string) {
    return this.httpClient.post<IAPIResponse>(`${baseUrl}/resend-email/${_id}`, null);
  }

  addUser(data: IAdminCollection) {
    const dataPass = {
      name: data.name,
      email: data.email,
      mobile: data.mobile,
      password: '',
    };
    return this.httpClient.post<IAPIResponse>(`${baseUrl}/user-register`, dataPass);
  }
  updatePassword(data: IGeneratePassword) {
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/update-generated-password/${data._id}`, data);
  }
}
