import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IAPIResponse, IPaginationResponse } from '../types/interfaces/api.interface';
import { IMessage } from '../types/interfaces/message.interface';

@Injectable({
  providedIn: 'root',
})
export class MessagesService {
  constructor(private httpClient: HttpClient) {}

  messageInsert(messageData: IMessage) {
    return this.httpClient.post<IAPIResponse<IMessage[]>>(`${baseUrl}/messages/`, messageData);
  }

  getMessages() {
    return this.httpClient.get<IAPIResponse<IMessage[]>>(`${baseUrl}/messages/`);
  }

  getMessagesByPage(currentPage: number, pageSize: number) {
    return this.httpClient.get<IAPIResponse<IPaginationResponse<IMessage[]>>>(
      `${baseUrl}/messages/page?currentPage=${currentPage}&pageSize=${pageSize}`,
    );
  }

  deleteComment(id: string, data: { userId: string; commentId: string }) {
    return this.httpClient.delete<IAPIResponse>(`${baseUrl}/messages/${id}/comment/${data.commentId}`);
  }

  deleteMessage(id: string) {
    return this.httpClient.delete<IAPIResponse>(`${baseUrl}/messages/${id}`);
  }
}
