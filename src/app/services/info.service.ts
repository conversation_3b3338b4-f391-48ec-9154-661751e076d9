import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IAPIResponse } from '../types/interfaces/api.interface';

@Injectable({
  providedIn: 'root',
})
export class InfoService {
  constructor(private httpClient: HttpClient) {}

  getInfo() {
    return this.httpClient.get<IAPIResponse>(`${baseUrl}/info`);
  }
}
