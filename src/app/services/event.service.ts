import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IAPIResponse, IPaginationResponse } from '../types/interfaces/api.interface';
import { IEvent } from '../types/interfaces/event.interface';

@Injectable({
  providedIn: 'root',
})
export class EventService {
  constructor(private httpClient: HttpClient) {}

  eventInsert(eventData: IEvent, image?: File | string) {
    const formData = new FormData();

    formData.append('title', eventData.title);
    formData.append('description', eventData.description);
    formData.append('startDate', eventData.startDate);
    formData.append('endDate', eventData.endDate);
    if (image) {
      formData.append('eventImage', image);
    }

    return this.httpClient.post<IAPIResponse<IEvent[]>>(`${baseUrl}/events/`, formData);
  }

  getEventsByPage(currentPage: number, pageSize: number) {
    return this.httpClient.get<IAPIResponse<IPaginationResponse<IEvent[]>>>(
      `${baseUrl}/event/page?currentPage=${currentPage}&pageSize=${pageSize}`,
    );
  }

  deleteEvent(id: string) {
    return this.httpClient.delete<IAPIResponse>(`${baseUrl}/events/${id}`);
  }
}
