import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';

import { baseUrl } from '../config/app.config';
import { IAPIResponse } from '../types/interfaces/api.interface';
import { IUserCollection } from '../types/interfaces/user.interface';

@Injectable({
  providedIn: 'root',
})
export class ProfileService {
  public editDataDetails: any = [];
  public subject = new Subject<any>();

  constructor(private httpClient: HttpClient) {}

  getUserById(id: string) {
    return this.httpClient.get<IAPIResponse<IUserCollection[]>>(`${baseUrl}/user/${id}`);
  }

  /*   getCarouselData() {
    return this.httpClient.get<IAPIResponse<IUserCollection[]>>(`${baseUrl}/carousel/`);
  } */

  updateUserDetails(id: string, editFormData: IUserCollection) {
    const updatedUserData = {
      name: editFormData.name,
      email: editFormData.email,
      mobile: editFormData.mobile,
    };

    return this.httpClient.put<IAPIResponse<IUserCollection[]>>(`${baseUrl}/user/${id}`, updatedUserData);
  }

  updateUserPassword(id: string, passwordFormData: IUserCollection) {
    const updatedUserPassword = {
      password: passwordFormData.newPassword,
    };

    return this.httpClient.put<IAPIResponse<IUserCollection[]>>(`${baseUrl}/user/${id}`, updatedUserPassword);
  }

  /* carouselUpload(carouselFormData, images: File[]) {
    const formData = new FormData();
    formData.append('title', carouselFormData.title);
    formData.append('description', carouselFormData.description);
    for (var i = 0; i < images.length; i++) {
      formData.append('image', images[i]);
    }

    return this.httpClient.post(`${baseUrl}/carousel`, formData);
  } */

  private getProfileMsg = new BehaviorSubject(this.editDataDetails);

  profileTrigger = this.getProfileMsg.asObservable();
  updateAdminName(message: string) {
    this.getProfileMsg.next(message);
  }
}
