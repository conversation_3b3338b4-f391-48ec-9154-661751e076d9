import { HttpClient, HttpHeaders, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { lastValueFrom, Observable } from 'rxjs';

import { baseUrl } from '../config/app.config';
import { SourceTypes } from '../types/enums/media.enum';
import { AWSResponse, IAPIResponse, IPaginationResponse } from '../types/interfaces/api.interface';
import { IMediaCollection, IPlay, IVimeoUploadUrlResponse } from '../types/interfaces/media.interface';
import { AuthUtils } from '../utils/auth';
enum Status {
  AVAILABLE = 'available',
}

@Injectable({
  providedIn: 'root',
})
export class MediaService {
  private vimeoURL = 'https://api.vimeo.com/videos';

  constructor(private httpClient: HttpClient) {}

  private getVimeoHeader() {
    const authStorage = AuthUtils.getAuthStorage();
    const vimeoToken = `Bearer ${authStorage?.vimeoId}`;
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', vimeoToken);
    return headers;
  }

  async uploadVideo(data: IMediaCollection, selectedIMage: File, selectedVideo: File) {
    const formData = new FormData();
    formData.append('title', data.title);
    formData.append('author', data.author);
    formData.append('description', data.description);
    formData.append('duration', data.duration);
    formData.append('channelId', data.channelId);
    formData.append('thumbnail', selectedIMage);

    const vimeoUrlData = {
      upload: {
        size: selectedVideo.size,
      },
      name: data.title,
      description: data.description,
    };
    const responseSubject = this.httpClient.post(`${baseUrl}/video/upload-url`, vimeoUrlData);

    const response = (await lastValueFrom(responseSubject)) as IAPIResponse<IVimeoUploadUrlResponse>;
    if (!response.status || !response.data) {
      return;
    }

    formData.append('vimeoId', response.data.vimeoId);
    const vimeoData = new FormData();
    vimeoData.append('file_data', selectedVideo);
    formData.append('status', response.data.status);
    try {
      const resp = await lastValueFrom(this.httpClient.post(response.data.upload.upload_link, vimeoData));
    } catch (e) {
      console.error(e);
    }

    return this.httpClient.post(`${baseUrl}/upload/video`, formData);
  }

  uploadAudio(data: IMediaCollection, selectedIMage: File, fileName: string, selectedAudio?: File) {
    const formData = new FormData();
    formData.append('title', data.title);
    formData.append('author', data.author);
    formData.append('description', data.description);
    formData.append('duration', data.duration);
    formData.append('channelId', data.channelId);
    formData.append('fileName', fileName);
    formData.append('thumbnail', selectedIMage);
    formData.append('status', Status.AVAILABLE);
    if (selectedAudio) {
      formData.append('upload', selectedAudio);
    }

    return this.httpClient.post(`${baseUrl}/upload/audio`, formData);
  }

  getAllVideos(currentPage: number, pageSize: number, sortBy: string, filterBy: string, search: string) {
    return this.httpClient.get<IAPIResponse<IPaginationResponse<IMediaCollection[]>>>(
      `${baseUrl}/videos?currentPage=${currentPage}&pageSize=${pageSize}&search=${search}&sort=${sortBy}&filter=${filterBy}`,
    );
  }

  getAllAudios(currentPage: number, pageSize: number, sortBy: string, filterBy: string, search: string) {
    return this.httpClient.get<IAPIResponse<IPaginationResponse<IMediaCollection[]>>>(
      `${baseUrl}/audios?currentPage=${currentPage}&pageSize=${pageSize}&search=${search}&filter=${filterBy}&sort=${sortBy}`,
    );
  }

  updateMedia(data: IMediaCollection) {
    const updateData = {
      title: data.title,
      author: data.author,
      description: data.description,
      channelId: data.channelId,
    };

    return this.httpClient.put(`${baseUrl}/upload/${data._id}`, updateData);
  }

  deleteMedia(id: string) {
    return this.httpClient.delete(`${baseUrl}/upload/${id}`);
  }

  getFilteredAudio(value: string) {
    return this.httpClient.get<IAPIResponse<IMediaCollection[]>>(`${baseUrl}/upload/audios/${value}`);
  }

  getFilteredVideo(value: string) {
    return this.httpClient.get<IAPIResponse<IMediaCollection[]>>(`${baseUrl}/upload/videos/${value}`);
  }

  getSignedUrl(fileName: string, fileType: string, type: SourceTypes) {
    return this.httpClient.post<IAPIResponse<string>>(`${baseUrl}/upload/upload-url`, { fileName, fileType, type });
  }

  uploadWithSignedUrl(url: string, contentType: string, file: FormData) {
    const headers = new HttpHeaders({ 'Content-Type': contentType });
    const req = new HttpRequest('PUT', url, file, { headers });
    return this.httpClient.request(req) as unknown as Observable<AWSResponse>;
  }

  playVideo(id: string) {
    return this.httpClient.get<IPlay>(`${this.vimeoURL}/${id}`, {
      headers: this.getVimeoHeader(),
    });
  }
}
