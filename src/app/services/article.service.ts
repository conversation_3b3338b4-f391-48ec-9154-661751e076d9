import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { forkJoin, map, of, switchMap } from 'rxjs';

import { baseUrl } from '../config/app.config';
import { IAPIResponse, IPaginationResponse } from '../types/interfaces/api.interface';
import { IArticleCollection, IArticleData } from '../types/interfaces/article';

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  constructor(private httpClient: HttpClient) {}

  createArticle(articleData: IArticleData, thumbnail?: File | string, articleImage?: File | string) {
    const thumbnailSource = thumbnail ? of(thumbnail) : this.getThumbnailImage();
    const articleSource = articleImage ? of(articleImage) : this.getArticleImage();
    return forkJoin([thumbnailSource, articleSource]).pipe(
      switchMap(([thumbnail, articleImage]) => {
        const formData = new FormData();

        formData.append('title', articleData.title);
        formData.append('author', articleData.author);
        formData.append('articleData', articleData.articleData);
        formData.append('published', JSON.stringify(articleData.published));

        formData.append('thumbnail', thumbnail);
        formData.append('articleImage', articleImage);

        return this.httpClient.post<IAPIResponse<IArticleCollection[]>>(`${baseUrl}/article/`, formData);
      }),
    );
  }

  updateArticle(articleData: IArticleData, thumbnail: File, articleImage: File) {
    const id = articleData._id;
    const formData = new FormData();

    formData.append('title', articleData.title);
    formData.append('author', articleData.author);
    formData.append('articleData', articleData.articleData);
    formData.append('published', JSON.stringify(articleData.published));

    if (thumbnail) {
      formData.append('thumbnail', thumbnail);
    }

    if (articleImage) {
      formData.append('articleImage', articleImage);
    }

    return this.httpClient.put(`${baseUrl}/article/${id}`, formData);
  }

  getArticle(currentPage: number, pageSize: number, search: string) {
    return this.httpClient.get<IAPIResponse<IPaginationResponse<IArticleCollection[]>>>(
      `${baseUrl}/article?currentPage=${currentPage}&pageSize=${pageSize}&search=${search}`,
    );
  }

  deleteArticle(_id: string) {
    return this.httpClient.delete<IAPIResponse>(`${baseUrl}/article/${_id}`);
  }

  getThumbnailImage() {
    return this.httpClient
      .get('assets/images/backgrounds/article-placeholder.png', {
        responseType: 'arraybuffer',
      })
      .pipe(
        map((response: any) => {
          return new File([response], 'thumbnail-default.png', { type: 'image/png' });
        }),
      );
  }

  getArticleImage() {
    return this.httpClient
      .get('assets/images/backgrounds/article-placeholder.png', {
        responseType: 'arraybuffer',
      })
      .pipe(
        map((response: any) => {
          return new File([response], 'article-default.png', { type: 'image/png' });
        }),
      );
  }
}
