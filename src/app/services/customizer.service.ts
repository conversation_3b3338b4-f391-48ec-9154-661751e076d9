import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CustomizerService {
  dir: any = 'ltr'; // Possible value rtl to make it right to left
  dark = false; // Possible value true
  minisidebar = true; // Possible value true
  horizontal = false; // Possible value true
  currentTheme = 'orangeTheme'; // Possible value orangeTheme, blueTheme, greenTheme, purpleTheme, indigoTheme, redTheme
  fixedTopbar = true; // Possible value true

  darkState = new Subject();
  public darktoggleState$ = this.darkState.asObservable();

  horizontalState = new Subject();
  public horizontaltoggle = this.horizontalState.asObservable();

  constructor() {}

  toggleDark() {
    this.dark = !this.dark;
    this.darkState.next(this.dark);
  }

  toggleHorizontal() {
    this.horizontal = !this.horizontal;
    this.horizontalState.next(this.horizontal);
  }

  setCurrentTheme(cvalue: any) {
    this.currentTheme = cvalue;
  }
}
