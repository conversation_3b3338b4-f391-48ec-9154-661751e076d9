import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IAPIResponse } from '../types/interfaces/api.interface';
import { IResetPasswordCollection } from '../types/interfaces/resetpassword.interface';

@Injectable({
  providedIn: 'root',
})
export class MemberService {
  constructor(private httpClient: HttpClient) {}

  tokenVerification(token: string) {
    return this.httpClient.post<IAPIResponse>(`${baseUrl}/token-verification`, { token: token });
  }

  resetPassword(data: IResetPasswordCollection) {
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/reset-password/verify`, data);
  }

  emailVerification(token: string) {
    const url = baseUrl.replace('/api/admin', '/api/members');
    return this.httpClient.post<IAPIResponse>(`${url}/verification`, { token: token });
  }
}
