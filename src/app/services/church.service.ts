import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IAPIResponse } from './../types/interfaces/api.interface';
import { IChurchCollection, ILoginChurchCollection } from './../types/interfaces/church.interface';

@Injectable({
  providedIn: 'root',
})
export class ChurchService {
  constructor(private httpClient: HttpClient) {}

  getChurches() {
    return this.httpClient.get<IAPIResponse<IChurchCollection[]>>(`${baseUrl}/churches`);
  }

  updateChurch(login: boolean) {
    const data = {
      login,
    };
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/church-login`, data);
  }

  getChurch() {
    return this.httpClient.get<IAPIResponse<ILoginChurchCollection>>(`${baseUrl}/church`);
  }
}
