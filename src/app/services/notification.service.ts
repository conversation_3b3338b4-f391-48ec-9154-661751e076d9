import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IAPIResponse, IPaginationResponse } from '../types/interfaces/api.interface';
import { INotification } from '../types/interfaces/notification.interface';

@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  constructor(private httpClient: HttpClient) {}

  getNotifications(currentPage: number, pageSize: number, start: number, end: number) {
    return this.httpClient.get<IAPIResponse<IPaginationResponse<INotification[]>>>(
      `${baseUrl}/notification?currentPage=${currentPage}&pageSize=${pageSize}&start=${start}&end=${end}`,
    );
  }

  notificationInsert(notificationData: INotification, image?: File | string) {
    const formData = new FormData();

    formData.append('title', notificationData.title);
    formData.append('content', notificationData.content);
    formData.append('author', notificationData.author);
    if (image) {
      formData.append('thumbnail', image);
    }

    return this.httpClient.post<IAPIResponse<INotification[]>>(`${baseUrl}/notifications/`, formData);
  }

  deleteNotification(id: string) {
    return this.httpClient.delete<IAPIResponse>(`${baseUrl}/notifications/${id}`);
  }
}
