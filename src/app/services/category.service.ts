import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IAPIResponse, IPaginationResponse } from '../types/interfaces/api.interface';
import { ICategoryCollection } from '../types/interfaces/category.interface';

@Injectable({
  providedIn: 'root',
})
export class CategoryService {
  constructor(private httpClient: HttpClient) {}

  getAllCategories(currentPage: number, pageSize: number, search: string) {
    return this.httpClient.get<IAPIResponse<IPaginationResponse<ICategoryCollection[]>>>(
      `${baseUrl}/channel?currentPage=${currentPage}&pageSize=${pageSize}&search=${search}`,
    );
  }

  getAllCategoriesByVideo() {
    return this.httpClient.get<IAPIResponse<ICategoryCollection[]>>(`${baseUrl}/channel/video`);
  }

  getAllCategoriesByAudio() {
    return this.httpClient.get<IAPIResponse<ICategoryCollection[]>>(`${baseUrl}/channel/audio`);
  }

  createCategory(data: ICategoryCollection, thumbnail: File) {
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('type', data.type);
    formData.append('thumbnail', thumbnail);
    return this.httpClient.post<IAPIResponse>(`${baseUrl}/channel`, formData);
  }

  updateCategory(data: ICategoryCollection, thumbnail: File) {
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('type', data.type);
    if (thumbnail) {
      formData.append('thumbnail', thumbnail);
    }

    return this.httpClient.put<IAPIResponse>(`${baseUrl}/channel/${data._id}`, formData);
  }

  deleteCategory(id: string) {
    return this.httpClient.delete<IAPIResponse>(`${baseUrl}/channel/${id}`);
  }
}
