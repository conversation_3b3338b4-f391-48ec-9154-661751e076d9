import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, ObservableInput, of, switchMap } from 'rxjs';

import { baseUrl } from '../config/app.config';
import { IAuthToken } from '../types/interfaces/auth.interface';
import { AuthUtils } from '../utils/auth';
import { IAPIResponse } from './../types/interfaces/api.interface';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private authenticated: boolean;

  constructor(private httpClient: HttpClient) {
    this.authenticated = false;
  }

  set accessToken(data: IAuthToken | null) {
    if (!data) {
      AuthUtils.clearAuth();
      return;
    }

    AuthUtils.setAuthStorage(data);
  }

  get accessToken(): IAuthToken | null {
    return AuthUtils.getAuthStorage();
  }

  signIn(credentials: { username: string; password: string }) {
    if (this.authenticated) {
      return of(true);
    }

    return this.httpClient.post(`${baseUrl}/login`, credentials).pipe(
      switchMap<any, ObservableInput<any>>((res: IAPIResponse<IAuthToken>) => {
        if (res.data) {
          this.accessToken = res.data;
          this.authenticated = true;
        }

        return of(res);
      }),
    );
  }

  signOut(): Observable<boolean> {
    AuthUtils.clearAuth();
    this.authenticated = false;
    return of(true);
  }

  check(): Observable<boolean> {
    try {
      if (this.authenticated) {
        return of(true);
      }

      if (AuthUtils.isTokenExpired()) {
        return of(false);
      }

      this.authenticated = true;
      return of(true);
    } catch (error) {
      this.signOut();
      return of(false);
    }
  }
}
