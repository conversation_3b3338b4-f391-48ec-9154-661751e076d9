import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IAPIResponse } from '../types/interfaces/api.interface';
import { IChartData } from '../types/interfaces/chart-options.interface';
import { IChurchCollection } from '../types/interfaces/church.interface';
import { IDashboardCount } from '../types/interfaces/dashboard.interface';

@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  constructor(private httpClient: HttpClient) {}

  getLoggedInUsersCount(type: string) {
    return this.httpClient.get<IAPIResponse<IChartData>>(`${baseUrl}/logged-in-users/${type}`);
  }

  getDashboardDataCount() {
    return this.httpClient.get<IAPIResponse<IDashboardCount>>(`${baseUrl}/count`);
  }

  getChurchAdminDetails() {
    return this.httpClient.get<IAPIResponse<IChurchCollection[]>>(`${baseUrl}/church-admin`);
  }
}
