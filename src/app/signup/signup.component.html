<div class="auth-wrapper flex-row flex-wrap">
  <!-- Left Section -->
  <div class="auth-left bg-white bg-church-landing-page">
    <div class="detail-part flex-row flex-center">
      <div class="text-center bg-church-landing-page-logo">
        <img src="{{ info.logo }}" appLogoOnError />
        <h2 class="m-t-10 fw-bold">{{ info.content | uppercase }}</h2>
      </div>
    </div>
  </div>

  <!-- Right Section -->
  <div class="auth-right flex-row flex-center">
    <div class="right-bg-content flex-row flex-wrap">
      <div class="form-container">
        <div class="p-30">
          <!-- <div class="auth-logo-main">
            <img
              src="https://tithing-apps-church-resources-prod.s3.amazonaws.com/629269b6ec901a6259e65460/logo/629269b6ec901a6259e65460.png"
              alt="logo" />
          </div> -->

          <h2 class="fw-bold m-b-5 text-primary">Welcome to {{ churchName }}</h2>

          <form [formGroup]="signupForm" class="m-t-30" (ngSubmit)="registerFormSubmit()">
            <mat-form-field appearance="outline">
              <mat-label>Name</mat-label>
              <input matInput name="name" formControlName="name" [readonly]="true" placeholder="Enter your name" />
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Email Address</mat-label>
              <input matInput name="email" formControlName="email" [readonly]="true" placeholder="Enter your email" />
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Mobile Number</mat-label>
              <input
                matInput
                name="mobileNumber"
                formControlName="mobile"
                [readonly]="true"
                placeholder="Enter your mobile number" />
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Password</mat-label>
              <input
                matInput
                formControlName="password"
                name="password"
                placeholder="Password"
                [type]="passwordHide ? 'password' : 'text'"
                [ngClass]="{ 'is-invalid': signupFormSubmitted && registerFormControl['password'].errors }" />
              <mat-icon class="text-primary mat-icon-no-color" matSuffix (click)="passwordHide = !passwordHide">
                {{ passwordHide ? 'visibility' : 'visibility_off' }}
              </mat-icon>
            </mat-form-field>

            <div *ngIf="signupFormSubmitted && registerFormControl['password'].errors" class="invalid-feedback">
              <div *ngIf="registerFormControl['password'].errors['required']">Password is required</div>
              <div *ngIf="registerFormControl['password'].errors['minlength']">
                Password must be at least 6 characters
              </div>
            </div>

            <mat-form-field appearance="outline">
              <mat-label>Confirm Password</mat-label>
              <input
                matInput
                formControlName="confirmPassword"
                name="confirmPassword"
                placeholder="Confirm Password"
                [type]="confirmPasswordHide ? 'password' : 'text'"
                [ngClass]="{ 'is-invalid': signupFormSubmitted && registerFormControl['confirmPassword'].errors }" />
              <mat-icon
                class="text-primary password-icon"
                matSuffix
                (click)="confirmPasswordHide = !confirmPasswordHide">
                {{ confirmPasswordHide ? 'visibility' : 'visibility_off' }}
              </mat-icon>
            </mat-form-field>

            <div *ngIf="signupFormSubmitted && registerFormControl['confirmPassword'].errors" class="invalid-feedback">
              <div *ngIf="registerFormControl['confirmPassword'].errors['required']">Confirm Password is required</div>
              <div *ngIf="registerFormControl['confirmPassword'].errors['matching']">
                Password and confirm password not match
              </div>
            </div>

            <button mat-flat-button color="primary" class="w-100" [class.spinner]="buttonLoading">Sign Up</button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
