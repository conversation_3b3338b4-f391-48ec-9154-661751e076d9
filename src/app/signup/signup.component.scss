/* Generic flex utilities */
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

/* Layout container */
.auth-wrapper {
  display: flex;
  flex-wrap: wrap;
  min-height: 100vh;
}

/* Left section */
.auth-left {
  flex: 1 1 100%;
  display: flex;
}

@media (min-width: 960px) {
  .auth-left {
    flex: 0 0 60%;
  }
}

/* Right section */
.auth-right {
  flex: 1 1 100%;
  display: flex;
  align-items: center;
}

@media (min-width: 960px) {
  .auth-right {
    flex: 0 0 40%;
  }
}

/* Inner parts */
.detail-part {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.right-bg-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}

.form-container {
  flex: 1 1 100%;
  max-width: 70%;
}

@media (min-width: 1280px) {
  .form-container {
    max-width: 50%;
  }
}

/* Common utility classes */
.text-center {
  text-align: center;
}

.text-primary {
  color: #3f51b5;
}

.text-success {
  color: #4caf50;
}

.fw-bold {
  font-weight: 600;
}

.m-t-10 {
  margin-top: 10px;
}

.m-t-30 {
  margin-top: 30px;
}

.m-b-5 {
  margin-bottom: 5px;
}

.w-100 {
  width: 100%;
}

.p-30 {
  padding: 30px;
}

.invalid-feedback {
  color: #f44336;
  font-size: 13px;
  margin-bottom: 8px;
}

.password-icon {
  cursor: pointer;
}
