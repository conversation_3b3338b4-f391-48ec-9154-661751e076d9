import { Component, OnInit } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';

import { AdminService } from '../services/admin.service';
import { CustomizerService } from '../services/customizer.service';
import { Utils } from '../utils';
import Validation from '../validators/confirmed.validator';
import { IChurchCollection, IInfo } from './../types/interfaces/church.interface';

@Component({
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.scss'],
  standalone: false,
})
export class SignupComponent implements OnInit {
  signupForm: UntypedFormGroup = new UntypedFormGroup({});
  signupFormSubmitted = false;

  success!: IChurchCollection;

  token!: string;
  churchName: string = '';
  info!: IInfo;
  passwordHide = true;
  confirmPasswordHide = true;
  buttonLoading: boolean = false;

  constructor(
    public snackBar: MatSnackBar,
    private route: ActivatedRoute,
    private router: Router,
    private adminService: AdminService,
    private fb: UntypedFormBuilder,
    public customizer: CustomizerService,
  ) {}

  ngOnInit(): void {
    this.info = Utils.getInfo();

    this.signupForm = this.fb.group(
      {
        name: ['', Validators.required],
        email: ['', [Validators.required]],
        mobile: ['', [Validators.required]],
        password: ['', [Validators.required, Validators.minLength(6)]],
        confirmPassword: ['', Validators.required],
      },
      {
        validators: [Validation.match('password', 'confirmPassword')],
      },
    );

    this.route.params.subscribe((params) => {
      this.token = params['token'] != undefined ? params['token'] : 0;
      this.getChurchTokenData(this.token);
    });
  }

  getChurchTokenData(token: string) {
    try {
      this.adminService.getChurchByToken(token).subscribe({
        next: (res) => {
          if (!res.status || !res.data) {
            this.openSnackBar('you are registered', 'logging in');
            this.goToLogin();
            return;
          }

          let response = <IChurchCollection>res.data[0];
          this.churchName = response.name;
          this.signupForm.patchValue({
            name: response.name,
            email: response.email,
            mobile: response.mobile,
          });
        },
        error: (err) => {
          this.openSnackBar(err.error.message, 'Try again');
          return 'error';
        },
      });
    } catch (error) {
      console.error(error);
    }
  }

  get registerFormControl(): { [key: string]: AbstractControl } {
    return this.signupForm.controls;
  }

  registerFormSubmit() {
    try {
      this.signupFormSubmitted = true;
      // stop here if form is invalid
      if (this.signupForm.invalid) {
        return;
      }

      this.success = this.signupForm.value;
      this.buttonLoading = true;
      this.adminService.updateChurchAdmin(this.token, this.success).subscribe({
        next: (res) => {
          if (!res.status) {
            this.buttonLoading = false;
            this.openSnackBar(res.message, 'Try again');
            return;
          }
          this.buttonLoading = false;

          this.openSnackBar('Signup Completed', 'Success');
          this.goToLogin();
        },
        error: (err) => {
          this.buttonLoading = false;

          this.openSnackBar(err.error.message, 'Try again');
          return 'error';
        },
      });
    } catch (error) {
      this.buttonLoading = false;

      console.error(error);
      this.openSnackBar('Something went wrong', 'Try again');
    }
  }

  goToLogin() {
    this.router.navigate(['/', 'login']);
  }

  openSnackBar(message: string, action: string) {
    this.snackBar.open(message, action, {
      duration: 1000,
    });
  }
}
