import { DatePipe } from '@angular/common';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { NgModule } from '@angular/core';
// import { NgxDatatableModule } from '@siemens/ngx-datatable';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTimepickerModule } from '@angular/material/timepicker';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
//import { NavService } from './services/nav.service';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { NgScrollbarModule } from 'ngx-scrollbar';

// import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
// import { PERFECT_SCROLLBAR_CONFIG } from 'ngx-perfect-scrollbar';
// import { PerfectScrollbarConfigInterface } from 'ngx-perfect-scrollbar';
import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { MessageComponent } from './components/message/message.component';
import { SpinnerModule } from './components/spinner/spinner.module';
import { AppImagOnErrorDirective } from './directives/logo-on-error.directive';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { AuthInterceptor } from './guard/auth.interceptor';
import { BlankComponent } from './layouts/blank/blank.component';
import { CustomizerComponent } from './layouts/full/customizer/customizer.component';
/**Components**/
import { FullComponent } from './layouts/full/full.component';
import { HorizontalHeaderComponent } from './layouts/full/header/horizontal-header/horizontal-header.component';
import { VerticalHeaderComponent } from './layouts/full/header/vertical-header/vertical-header.component';
import { LogoComponent } from './layouts/full/logo/logo.component';
import { HorizontalSidebarComponent } from './layouts/full/sidebar/horizontal-sidebar/horizontal-sidebar.component';
import { Hnavbar } from './layouts/full/sidebar/horizontal-sidebar/navbar/hnavbar.component';
import { MenuListItemComponent } from './layouts/full/sidebar/vertical-sidebar/menu-list-item/menu-list-item.component';
import { VerticalSidebarComponent } from './layouts/full/sidebar/vertical-sidebar/vertical-sidebar.component';
import { LoginComponent } from './login/login.component';
import { MaterialModule } from './material/material.module';
import { InvalidDomainComponent } from './pages/invalid-domain/invalid-domain.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { AuthService } from './services/auth.service';
import { CustomizerService } from './services/customizer.service';
import { SignupComponent } from './signup/signup.component';

// const DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {
//   suppressScrollX: true,
//   wheelSpeed: 2,
//   wheelPropagation: true,
// };

@NgModule({
  declarations: [
    AppComponent,
    FullComponent,
    BlankComponent,
    VerticalHeaderComponent,
    HorizontalHeaderComponent,
    VerticalSidebarComponent,
    Hnavbar,
    MenuListItemComponent,
    HorizontalSidebarComponent,
    CustomizerComponent,
    LogoComponent,
    LoginComponent,
    SignupComponent,
    ResetPasswordComponent,
    ForgotPasswordComponent,
    AppImagOnErrorDirective,
    InvalidDomainComponent,
    MessageComponent,
  ],
  bootstrap: [AppComponent],
  imports: [
    SpinnerModule,
    BrowserModule,
    AppRoutingModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FeatherModule.pick(allIcons),
    // FlexLayoutModule,
    BrowserAnimationsModule,
    NgScrollbarModule,
    MatTimepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    CKEditorModule,
    MatChipsModule,
  ],
  exports: [MatChipsModule],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    CustomizerService,
    DatePipe,
    AuthService,
    provideHttpClient(withInterceptorsFromDi()),
  ],
})
export class AppModule {}
