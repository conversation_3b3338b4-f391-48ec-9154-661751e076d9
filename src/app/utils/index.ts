import { IInfo } from '../types/interfaces/church.interface';

export class Utils {
  static getStorage(key: string): Object | null {
    try {
      const str = localStorage.getItem(key);
      if (!str) {
        return null;
      }

      const result = JSON.parse(str);
      return result;
    } catch (err) {
      console.log(err);
      return null;
    }
  }

  static setStorage(key: string, str: Object): boolean {
    try {
      localStorage.setItem(key, JSON.stringify(str));
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  static clearStorage(key: string) {
    localStorage.removeItem(key);
  }

  static defaultLogo = 'https://tithing-apps-church-resources-prod.s3.amazonaws.com/tithing/logo.png';

  static getInfo(): IInfo {
    const church = Utils.getStorage('church') as IInfo | null;
    if (!church) {
      return { churchId: '', logo: this.defaultLogo, content: '', name: '' };
    }

    return { ...church, logo: church.logo || this.defaultLogo };
  }
}
