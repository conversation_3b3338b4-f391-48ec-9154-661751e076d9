// authentication other pages
@media (min-width: 1200px) {
  .left-bg-img {
    position: absolute;
    height: 100vh;
    right: -50px;
  }
}

.left-bg-img {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px auto;
}

.auth-wrapper {
  box-sizing: border-box;
  display: flex;
  flex-flow: row wrap;
  width: 100%;
  height: 100vh;
  -webkit-box-pack: center;
  justify-content: center;
}

.right-bg-content {
  box-sizing: border-box;
  flex-flow: row wrap;
  width: 100%;
  display: flex;
  -webkit-box-pack: center;
  justify-content: center;
}

.divider-text {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 110px;
  top: -11px;
  background: #fafafa;
  display: block;
  text-align: center;
}
/* === Loading Spinner === */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/* === Main Layout === */
.auth-wrapper {
  display: flex;
  flex-wrap: wrap;
  min-height: 100vh;
  width: 100%;
}

/* === Left Side === */
.auth-left {
  flex: 1 1 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-position: center;
}

@media (min-width: 960px) {
  .auth-left {
    flex: 0 0 60%;
  }
}

.detail-part {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* === Right Side === */
.auth-right {
  flex: 1 1 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 960px) {
  .auth-right {
    flex: 0 0 40%;
  }
}

.right-bg-content {
  display: flex;
  justify-content: center;
  width: 100%;
  flex-wrap: wrap;
}

.auth-form-container {
  flex: 1 1 100%;
  max-width: 500px;
}

@media (min-width: 600px) {
  .auth-form-container {
    flex: 0 0 70%;
  }
}

@media (min-width: 1200px) {
  .auth-form-container {
    flex: 0 0 70%;
  }
}

/* === Utility Classes === */
.text-center {
  text-align: center;
}

.w-100 {
  width: 100%;
}

.fw-bold {
  font-weight: 700;
}

.text-primary {
  color: var(--mat-primary, #3f51b5);
}

.text-accent {
  color: var(--mat-accent, #ff4081);
}

.text-white {
  color: #fff;
}

.bg-danger {
  background-color: #dc3545;
}

.p-10 {
  padding: 10px;
}

.p-30 {
  padding: 30px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-t-30 {
  margin-top: 30px;
}

.m-b-5 {
  margin-bottom: 5px;
}

.m-b-15 {
  margin-bottom: 15px;
}

/* === Forgot Password Link === */
.forgot-link {
  display: flex;
  margin: 10px 0 15px 0;
}

/* === Spinner Disable === */
.spinner {
  opacity: 0.6;
  pointer-events: none;
}

/* === Validation === */
.invalid-feedback {
  color: #d9534f;
  font-size: 0.9rem;
  margin-top: -8px;
  margin-bottom: 10px;
}
