import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Route, Router, RouterStateSnapshot, UrlSegment, UrlTree } from '@angular/router';
import { Observable, of, switchMap } from 'rxjs';

import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard {
  constructor(private authService: AuthService, private router: Router) {}

  private check(redirectURL: string): Observable<boolean> {
    return this.authService.check().pipe(
      switchMap((authenticated) => {
        if (!authenticated) {
          this.router.navigate(['login'], { queryParams: { redirectURL } });
          return of(false);
        }

        return of(true);
      }),
    );
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean> | Promise<boolean> | boolean {
    let redirectUrl = state.url;

    if (redirectUrl === '/logout') {
      redirectUrl = '/';
    }

    return this.check(redirectUrl);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    let redirectUrl = state.url;

    if (redirectUrl === '/logout') {
      redirectUrl = '/';
    }

    return this.check(redirectUrl);
  }

  canLoad(route: Route, segments: UrlSegment[]): Observable<boolean> | Promise<boolean> | boolean {
    return this.check('/');
  }
}
