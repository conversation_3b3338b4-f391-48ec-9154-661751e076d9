/* You can add global styles to this file, and also import other style files */

html,
body {
  height: 100%;
}

body {
  margin: 0;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.cursor-pointer,
.mat-button-wrapper {
  cursor: pointer !important;
}

input[type='file'],
input[type='file']::-webkit-file-upload-button {
  opacity: 0;
  cursor: pointer !important;
  width: 100%;
  position: absolute;
  left: 0;
  height: 100%;
}

.mat-button-wrapper > label,
.mat-button-wrapper > .ng-star-inserted > label {
  cursor: pointer !important;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.invisible {
  opacity: 0 !important;
}

.spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  border-top-color: #fb9778;
  animation: spinner 0.8s linear infinite;
}

.auth-brand {
  position: absolute;
  top: 0;
  img {
    width: 90px;
  }
}

.auth-logo-main {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  img {
    width: 150px;
    height: 150px;
  }
}

.bg-church-landing-page {
  background-color: #2f2e29 !important;
  background-size: cover;
}

.bg-church-landing-page-logo {
  img {
    width: 250px;
    text-align: center;
  }
  h2 {
    color: #fefcff;
  }
}

.upload-image {
  width: 140px;
  height: 140px;
}

.mat-card-content-custom {
  padding: 26px 26px 15px 26px !important;
}

.mat-card-title-custom-cls {
  line-height: 1;
}

.list-card-left-image-custom {
  width: 150px;
  height: 200px;
  object-fit: fill;
}

.list-card-custom {
  height: 200px !important;
}

.image-informative-cls {
  margin: 4px 0px 0px 15px;
  font-size: small;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.half-width {
  flex: 0 0 50%;
}
.flex-row-wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.cke_notification_warning{
  display: none !important;
}
.flex-spacer {
  flex: 1 1 auto;
}
mat-toolbar {
  display: flex;
  align-items: center;
}
.orange-primary{
  background-color: #fb9778 !important;
  border-radius: 4px !important;
  color: #fff !important;
}
.orange-primary.mat-mdc-button-disabled{
  background-color: #0000001f !important;
  color: #00000042 !important;
}
.page-wrapper{
  background-color: #fafafa !important;
  color: #000000de;
}
.main-container .mat-mdc-card {
    border-radius: 20px;
    box-shadow: 1px 0 20px #00000014 !important;
    padding: 0;
    margin: 15px;
    overflow: hidden;
    background: #fff;
}
.mat-divider{
      border-top-color: #e3dbe9 !important;
}
.titleheads{
  font-weight: 500 !important;
}
.button-round{
  border-radius: 50% !important;
  background: #fff !important;
  color: #000 !important;
}
.search-input .mat-mdc-form-field{
  width: 35%;
} 

.search-input .mat-mdc-text-field-wrapper{
  background-color: #fff !important;
  padding: 0px !important;
  font-size: 14px;
}
.search-input .mat-mdc-form-field-infix{
  padding-top: 25px !important;
  padding-bottom: 0px !important;
}
.btn-blue{
  background-color: #03c9d7 !important;
  border-radius: 4px !important;
  box-shadow: 0 0 #0003, 0 0 #00000024, 0 0 #0000001f;
  color: #fff !important;
}

.mat-mdc-text-field-wrapper{
  background-color: #fff !important;
  padding: 0px !important;
}
.mat-mdc-form-field{
  width: 100%;
}
.mdc-button{
  border-radius: 4px !important;
}
.mat-mdc-form-field-infix{
  padding-bottom:0px !important;
  padding-top: 25px !important;
}
.mat-mdc-dialog-actions{
  justify-content: left !important;
}
.text-btn{
  color: #000 !important;
}
.block{
  display: block;
}
.mat-mdc-notch-piece.mdc-notched-outline__trailing, .mat-mdc-notch-piece.mdc-notched-outline__notch, .mat-mdc-notch-piece.mdc-notched-outline__leading{
  border: unset !important;
  border-bottom: 1px solid !important;
}
.delete-text{
  padding: 0px 24px;
}