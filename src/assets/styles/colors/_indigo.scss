@use '@angular/material' as mat;

// Create light theme using M3 theming
$indigo-app-theme: mat.define-theme((
  color: (
    theme-type: light,
    primary: mat.$azure-palette,
  ),
));

// Create dark theme using M3 theming
$indigo-app-dark-theme: mat.define-theme((
  color: (
    theme-type: dark,
    primary: mat.$azure-palette,
  ),
));

.leftsidebar .menu-list-item.activeMenu {
  .mat-list-item-content,
  &:hover .mat-list-item-content {
    background-color: mat.get-theme-color($indigo-app-theme, primary, 40);
  }
}
