@use '@angular/material' as mat;

// Create light theme using M3 theming
$green-app-theme: mat.define-theme((
  color: (
    theme-type: light,
    primary: mat.$green-palette,
    tertiary: mat.$cyan-palette,
  ),
));

// Create dark theme using M3 theming
$green-app-dark-theme: mat.define-theme((
  color: (
    theme-type: dark,
    primary: mat.$green-palette,
    tertiary: mat.$cyan-palette,
  ),
));

.leftsidebar .menu-list-item.activeMenu {
  .mat-list-item-content,
  &:hover .mat-list-item-content {
    background-color: mat.get-theme-color($green-app-theme, primary, 40);
  }
}
