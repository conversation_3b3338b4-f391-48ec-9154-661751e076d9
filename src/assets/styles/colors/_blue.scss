@use '@angular/material' as mat;
@use '../variables' as vars;

// Create light theme using M3 theming
$blue-app-theme: mat.define-theme((
  color: (
    theme-type: light,
    primary: mat.$blue-palette,
  ),
));

// Create dark theme using M3 theming
$blue-app-dark-theme: mat.define-theme((
  color: (
    theme-type: dark,
    primary: mat.$blue-palette,
  ),
));

// Example of applying accent color in custom styles
.leftsidebar .menu-list-item.activeMenu {
  .mat-list-item-content,
  &:hover .mat-list-item-content {
    background-color: mat.get-theme-color($blue-app-theme, primary, 40);
  }
}
