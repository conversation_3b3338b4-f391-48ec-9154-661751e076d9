@use '@angular/material' as mat;

// Create light theme using M3 theming
$purple-app-theme: mat.define-theme((
  color: (
    theme-type: light,
    primary: mat.$violet-palette,
  ),
));

// Create dark theme using M3 theming
$purple-app-dark-theme: mat.define-theme((
  color: (
    theme-type: dark,
    primary: mat.$violet-palette,
  ),
));

.leftsidebar .menu-list-item.activeMenu {
  .mat-list-item-content,
  &:hover .mat-list-item-content {
    background-color: mat.get-theme-color($purple-app-theme, primary, 40);
  }
}
