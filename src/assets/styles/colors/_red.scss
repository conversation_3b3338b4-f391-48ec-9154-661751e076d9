@use '@angular/material' as mat;

// Create light theme using M3 theming
$red-app-theme: mat.define-theme((
  color: (
    theme-type: light,
    primary: mat.$red-palette,
  ),
));

// Create dark theme using M3 theming
$red-app-dark-theme: mat.define-theme((
  color: (
    theme-type: dark,
    primary: mat.$red-palette,
  ),
));

.leftsidebar .menu-list-item.activeMenu {
  .mat-list-item-content,
  &:hover .mat-list-item-content {
    background-color: mat.get-theme-color($red-app-theme, primary, 40);
  }
}
