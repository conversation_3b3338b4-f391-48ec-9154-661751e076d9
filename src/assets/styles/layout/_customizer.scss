@use '../variables' as vars;

.customizerSidebar {
  width: vars.$customizerwidth;
}
.customizerbtn.mat-fab {
  position: fixed;
  bottom: 15px;
  z-index: 1;
}
.ltr .customizerbtn.mat-fab {
  right: 25px;
}
.rtl .customizerbtn.mat-fab {
  left: 25px;
}

.themeColorOption {
  padding: 15px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  li {
    list-style: none;
    padding: 5px;
    line-height: 0;
    color: #fff;
    cursor: pointer;
    border-radius: 100%;
    &.orangeTheme {
      background-color: vars.$primary;
    }
    &.greenTheme {
      background-color: vars.$success;
    }
    &.blueTheme {
      background-color: vars.$info;
    }
    &.redTheme {
      background-color: vars.$danger;
    }
    &.indigoTheme {
      background-color: vars.$indigo;
    }
    &.purpleTheme {
      background-color: vars.$purple;
    }
  }
}
.bg-light-pink-shade {
  background-color: #c27ba0 !important;
}
