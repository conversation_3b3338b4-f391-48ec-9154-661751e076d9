@use '../variables' as vars;

.topbar {
  .topsearch {
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 1;
    width: 100%;
    padding: 15px;
    height: 64px;
  }
  .mat-textsmall {
    font-size: 16px;
  }
  .mat-form-field-appearance-fill .mat-form-field-flex {
    background-color: transparent;
  }
}
.fixedTopbar .main-container {
  .topbar {
    position: sticky;
    z-index: 10;
    position: -webkit-sticky;
    top: 0;
    display: block;
  }
}
.lightTheme {
  .topsearch,
  .topbar .mat-toolbar {
    background: vars.$bodylightbg;
  }
}
.darkTheme {
  .topsearch,
  .topbar .mat-toolbar {
    background: vars.$bodydarkbg;
  }
}

.topbardd {
  width: 385px;
  max-width: 385px !important;
  padding: 10px 20px 20px;
  background-color: #fff !important;
  .mat-list-base {
    .mat-list-item {
      .mat-list-icon {
        &.mat-list-item-img {
          width: 45px;
          height: 45px;
          line-height: 50px;
          text-align: center;
          img {
            border-radius: 100%;
          }
        }
      }
    }
  }
  .ddheadtitle {
    font-size: 18px;
  }
  .ddtitle {
    font-weight: 500 !important;
  }
}

.notify {
  position: absolute;
  top: 6px;
  right: 4px;
  .point {
    width: 8px;
    height: 8px;
    border-radius: 100%;
    display: block;
  }
}
.button-style{
  background-color: #03c9d7 !important;
  box-shadow: 0 0 #0003, 0 0 #00000024, 0 0 #0000001f;
  box-sizing: border-box;
    position: relative;
    -webkit-user-select: none;
    user-select: none;
    cursor: pointer;
    outline: none;
    border: none;
    -webkit-tap-highlight-color: transparent;
    display: inline-block;
    white-space: nowrap;
    text-decoration: none;
    vertical-align: baseline;
    text-align: center !important;
    margin: 0;
    min-width: 64px !important;
    line-height: 36px !important;
    padding: 0 16px !important;
    border-radius: 4px !important;
    overflow: visible;
}