@use '../variables' as vars;

.horizontal {
  .page-wrapper {
    margin-left: 0 !important;
  }
}

/* Main Sidebar Styles */
.leftsidebar {
  width: 80px;
  box-shadow: vars.$box-shadow;
  border: 0px;
  transition: width 0.3s ease;
  background: #ffffff;
  overflow: visible;
}

/* Hover Expansion */
.leftsidebar:hover {
  width: 260px;
}

/* Sidebar Content */
.vsidebar {
  padding: 0;
  height: 100%;

  .vmenu-list-item {
    padding: 8px;
  }

  .routeIcon {
    width: 20px;
    height: 20px;
    font-size: 20px;
    flex-shrink: 0;
  }

  .menu-list-item {
    margin: 0 0 6px 0;
    border-radius: 8px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    padding: 0 12px;

    &:hover {
      background-color: #f5f7f9;
    }

    &.activeMenu {
      background-color: #00c2c7;
      color: #ffffff;

      .routeIcon {
        color: #ffffff;
      }
    }
  }
}

/* Horizontal Sidebar */
.hsidebar {
  padding: 0 8px;
}

/* Mini Sidebar */
.minisidebar {
  .leftsidebar {
    width: 80px;
  }
}

// Active menu styling with cyan/turquoise background
.vsidebar .menu-list-item.activeMenu,
.hsidebar .menu-list-item.activeMenu {
  .mat-list-item-content,
  &:hover .mat-list-item-content {
    color: vars.$white;
    background: linear-gradient(135deg, #00bcd4, #26c6da); // Cyan gradient
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }
}

// Icon-only specific styles
.menu-list-item.icon-only {
  .mat-list-item-content {
    position: relative;
    overflow: visible;
  }

  // Tooltip on hover
  &:hover::after {
    content: attr(title);
    position: absolute;
    left: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

// Compact sidebar specific styles
.compact-sidebar {
  .logo-container {
    padding: 16px 8px;
    display: flex;
    justify-content: center;
    align-items: center;

    .compact-logo {
      padding: 0 !important;

      // Hide text, show only logo image
      .logo-text {
        display: none;
      }

      .logo-img {
        width: 32px;
        height: 32px;
        margin: 0 auto;
      }
    }
  }

  .compact-menu {
    padding: 0;

    .mat-mdc-list-base {
      padding: 0;
    }
  }
}

// Override Material Design list styles for compact mode
.compact-sidebar .vmenu-list-item {
  .mat-mdc-list-item {
    height: 56px;
    padding: 0;
    margin-bottom: 8px;

    .mdc-list-item__content {
      padding: 0;
    }
  }
}
