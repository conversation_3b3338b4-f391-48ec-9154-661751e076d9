@use '../variables' as vars;

.horizontal {
  .page-wrapper {
    margin-left: 0 !important;
  }
}
.leftsidebar {
  width: 80px; // Compact sidebar width
  box-shadow: vars.$box-shadow;
  border: 0px;
  transition: width 0.3s ease;
}

.vsidebar,
.hsidebar {
  padding: 0 8px;

  .routeIcon {
    width: 24px;
    height: 24px;
    margin: 0 auto;
    display: block;
  }

  .menu-list-item {
    margin: 0 0 8px 0;
    display: flex;
    justify-content: center;
    align-items: center;

    .mat-list-item-content {
      border-radius: 12px;
      padding: 12px;
      min-height: 48px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 48px;
      height: 48px;
    }

    &:hover {
      background-color: transparent;
      .mat-list-item-content {
        background-color: rgba(0, 0, 0, 0.08);
      }
    }

    // Hide text labels in compact mode
    .hide-menu {
      display: none;
    }

    // Hide arrow icons for children
    .arrow-icon {
      display: none;
    }
  }
}

.minisidebar {
  .leftsidebar {
    width: 80px;
  }
}

// Active menu styling with cyan/turquoise background
.vsidebar .menu-list-item.activeMenu,
.hsidebar .menu-list-item.activeMenu {
  .mat-list-item-content,
  &:hover .mat-list-item-content {
    color: vars.$white;
    background: linear-gradient(135deg, #00bcd4, #26c6da); // Cyan gradient
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }
}

// Icon-only specific styles
.menu-list-item.icon-only {
  .mat-list-item-content {
    position: relative;
    overflow: visible;
  }

  // Tooltip on hover
  &:hover::after {
    content: attr(title);
    position: absolute;
    left: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

// Compact sidebar specific styles
.compact-sidebar {
  .logo-container {
    padding: 16px 8px;
    display: flex;
    justify-content: center;
    align-items: center;

    .compact-logo {
      padding: 0 !important;

      // Hide text, show only logo image
      .logo-text {
        display: none;
      }

      .logo-img {
        width: 32px;
        height: 32px;
        margin: 0 auto;
      }
    }
  }

  .compact-menu {
    padding: 0;

    .mat-mdc-list-base {
      padding: 0;
    }
  }
}

// Override Material Design list styles for compact mode
.compact-sidebar .vmenu-list-item {
  .mat-mdc-list-item {
    height: 56px;
    padding: 0;
    margin-bottom: 8px;

    .mdc-list-item__content {
      padding: 0;
    }
  }
}
