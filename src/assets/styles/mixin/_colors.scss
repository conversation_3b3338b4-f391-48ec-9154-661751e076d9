@use 'sass:meta';
@use '../variables' as vars;

@mixin syntax-colors($args...) {
  @each $name, $color in meta.keywords($args) {
    .bg-#{$name} {
      background-color: $color !important;
    }
    .text-#{$name} {
      color: $color !important;
    }
  }
}

@include syntax-colors(
  $primary: vars.$primary,
  $accent: vars.$accent,
  $success: vars.$success,
  $warning: vars.$warning,
  $danger: vars.$danger,
  $info: vars.$info,
  $white: vars.$white,
  $muted: vars.$muted,
  $light: vars.$light,
  $light-danger: vars.$light-danger,
  $light-accent: vars.$light-accent,
  $light-success: vars.$light-success,
  $light-warning: vars.$light-warning,
  $light-primary: vars.$light-primary,
  $light-info: vars.$light-info,
  $light-inverse: vars.$light-inverse,
  $light-megna: vars.$light-megna
);
