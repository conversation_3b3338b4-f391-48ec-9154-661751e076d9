//  Import Material v18+ Sass API
@use '@angular/material' as mat;

// Your app-level variable and layout partials
@use 'variables';
@use './mixin/spacing';
@use './mixin/colors';
@use './layout/sidebar';
@use './layout/topbar';
@use './layout/overrides';
@use './layout/customizer';
@use './layout/container';
@use './layout/horizontal';
@use './layout/minisidebar';
@use './layout/widget';
@use './layout/rtl';
@use './layout/dark';
@use './pages/tables';
@use './pages/authentication';
@use 'colors/blue' as bl;
@use 'colors/green' as gr;
@use 'colors/purple' as pp;
@use 'colors/red' as rd;
@use 'colors/indigo' as id;

// Include Material core styles (only once)
@include mat.core();

//  Theme variants - only include color overrides
.lightTheme {
  .blueTheme {
    @include mat.all-component-colors(bl.$blue-app-theme);
  }
  .greenTheme {
    @include mat.all-component-colors(gr.$green-app-theme);
  }
  .purpleTheme {
    @include mat.all-component-colors(pp.$purple-app-theme);
  }
  .redTheme {
    @include mat.all-component-colors(rd.$red-app-theme);
  }
  .indigoTheme {
    @include mat.all-component-colors(id.$indigo-app-theme);
  }
}

.darkTheme {
  .blueTheme {
    @include mat.all-component-colors(bl.$blue-app-dark-theme);
  }
  .greenTheme {
    @include mat.all-component-colors(gr.$green-app-dark-theme);
  }
  .purpleTheme {
    @include mat.all-component-colors(pp.$purple-app-dark-theme);
  }
  .redTheme {
    @include mat.all-component-colors(rd.$red-app-dark-theme);
  }
  .indigoTheme {
    @include mat.all-component-colors(id.$indigo-app-dark-theme);
  }
}
