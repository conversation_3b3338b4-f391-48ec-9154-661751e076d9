{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"Church-APP-Admin": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.gif", "src/assets", {"glob": "**/*", "input": "./node_modules/@kolkov/angular-editor/assets/", "output": "./assets/fonts/"}], "styles": ["node_modules/angular-calendar/css/angular-calendar.css", "src/assets/styles/style.scss", "src/styles.scss"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js"], "browser": "src/main.ts", "allowedCommonJsDependencies": ["moment", "@ckeditor/ckeditor5-build-classic", "apexcharts", "crypto-js"]}, "configurations": {"stage": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "10mb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "outputHashing": "all"}, "production": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "10mb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "Church-APP-Admin:build", "port": 4200, "disableHostCheck": true}, "configurations": {"production": {"buildTarget": "Church-APP-Admin:build:production"}, "development": {"buildTarget": "Church-APP-Admin:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "Church-APP-Admin:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.gif", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}}